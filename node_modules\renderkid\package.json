{"_from": "renderkid@^2.0.4", "_id": "renderkid@2.0.7", "_inBundle": false, "_integrity": "sha512-oCcFyxaMrKsKcTY59qnCAtmDVSLfPbrv6A3tVbPdFMMrv5jaK10V6m40cKsoPNhAqN6rmHW9sswW4o3ruSrwUQ==", "_location": "/renderkid", "_phantomChildren": {"boolbase": "1.0.0", "domhandler": "4.3.1", "entities": "2.2.0"}, "_requested": {"type": "range", "registry": true, "raw": "renderkid@^2.0.4", "name": "renderkid", "escapedName": "renderkid", "rawSpec": "^2.0.4", "saveSpec": null, "fetchSpec": "^2.0.4"}, "_requiredBy": ["/pretty-error"], "_resolved": "http://*************:4873/renderkid/-/renderkid-2.0.7.tgz", "_shasum": "464f276a6bdcee606f4a15993f9b29fc74ca8609", "_spec": "renderkid@^2.0.4", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\pretty-error", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/AriaMinaei/RenderKid/issues"}, "bundleDependencies": false, "dependencies": {"css-select": "^4.1.3", "dom-converter": "^0.2.0", "htmlparser2": "^6.1.0", "lodash": "^4.17.21", "strip-ansi": "^3.0.1"}, "deprecated": false, "description": "Stylish console.log for node", "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-env": "^7.14.5", "chai": "^4.3.4", "chai-changes": "^1.3.6", "chai-fuzzy": "^1.6.1", "coffeescript": "^2.5.1", "mocha": "^9.0.0", "sinon": "^11.1.1", "sinon-chai": "^3.7.0"}, "homepage": "https://github.com/AriaMinaei/RenderKid#readme", "license": "MIT", "main": "lib/RenderKid.js", "name": "renderkid", "repository": {"type": "git", "url": "git+https://github.com/AriaMinaei/RenderKid.git"}, "scripts": {"compile": "coffee --bare --transpile --output ./lib ./src", "compile:watch": "coffee --watch --bare --transpile --output ./lib ./src", "prepublish": "npm run compile", "test": "mocha \"test/**/*.coffee\"", "test:watch": "npm run test -- --watch", "watch": "npm run compile:watch & npm run test:watch"}, "version": "2.0.7"}