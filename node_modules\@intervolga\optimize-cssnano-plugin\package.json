{"_from": "@intervolga/optimize-cssnano-plugin@^1.0.5", "_id": "@intervolga/optimize-cssnano-plugin@1.0.6", "_inBundle": false, "_integrity": "sha512-zN69TnSr0viRSU6cEDIcuPcP67QcpQ6uHACg58FiN9PDrU6SLyGW3MR4tiISbYxy1kDWAVPwD+XwQTWE5cigAA==", "_location": "/@intervolga/optimize-cssnano-plugin", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "@intervolga/optimize-cssnano-plugin@^1.0.5", "name": "@intervolga/optimize-cssnano-plugin", "escapedName": "@intervolga%2foptimize-cssnano-plugin", "scope": "@intervolga", "rawSpec": "^1.0.5", "saveSpec": null, "fetchSpec": "^1.0.5"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://192.168.3.141:4873/@intervolga/optimize-cssnano-plugin/-/optimize-cssnano-plugin-1.0.6.tgz", "_shasum": "be7c7846128b88f6a9b1d1261a0ad06eb5c0fdf8", "_spec": "@intervolga/optimize-cssnano-plugin@^1.0.5", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "Sh<PERSON>upa Alex"}, "bugs": {"url": "https://github.com/intervolga/optimize-cssnano-plugin/issues"}, "bundleDependencies": false, "dependencies": {"cssnano": "^4.0.0", "cssnano-preset-default": "^4.0.0", "postcss": "^7.0.0"}, "deprecated": false, "description": "WebPack 2+ plugin for CSS minification after ExtractTextPluging", "devDependencies": {"autoprefixer": "^8.6.5", "css-loader": "^0.28.11", "eslint": "^4.19.1", "eslint-config-google": "^0.8.0", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "expect.js": "^0.3.1", "extract-text-webpack-plugin": "^4.0.0-beta.0", "fs-extra": "^5.0.0", "mocha": "^5.2.0", "node-sass": "^4.9.2", "postcss-loader": "^2.1.6", "sass-loader": "^6.0.7", "style-loader": "^0.20.3", "webpack": "^4.16.1"}, "files": ["lib", "index.js", "README", "LICENSE"], "homepage": "https://github.com/intervolga/optimize-cssnano-plugin#readme", "keywords": ["html", "index", "webpack", "loader"], "license": "MIT", "main": "index.js", "name": "@intervolga/optimize-cssnano-plugin", "peerDependencies": {"webpack": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/intervolga/optimize-cssnano-plugin.git"}, "scripts": {"lint": "eslint index.js lib test/index.js test/helpers", "mocha": "mocha --ui tdd test/", "test": "npm run lint && npm run mocha"}, "version": "1.0.6"}