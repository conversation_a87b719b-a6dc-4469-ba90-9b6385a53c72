{"_from": "domelementtype@^2.2.0", "_id": "domelementtype@2.3.0", "_inBundle": false, "_integrity": "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==", "_location": "/domhandler/domelementtype", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "domelementtype@^2.2.0", "name": "domelementtype", "escapedName": "domelementtype", "rawSpec": "^2.2.0", "saveSpec": null, "fetchSpec": "^2.2.0"}, "_requiredBy": ["/domhandler"], "_resolved": "http://*************:4873/domelementtype/-/domelementtype-2.3.0.tgz", "_shasum": "5c45e8e869952626331d7aab326d01daf65d589d", "_spec": "domelementtype@^2.2.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\domhandler", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/domelementtype/issues"}, "bundleDependencies": false, "deprecated": false, "description": "all the types of nodes in htmlparser2's dom", "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "prettier": "^2.6.2", "typescript": "^4.6.3"}, "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "files": ["lib/**/*"], "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "homepage": "https://github.com/fb55/domelementtype#readme", "keywords": ["dom", "element", "types", "htmlparser2"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "module": "lib/esm/index.js", "name": "domelementtype", "prettier": {"tabWidth": 4}, "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc", "build:esm": "tsc --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "format": "prettier --write **/*.{ts,json,md}", "lint": "eslint src", "prepare": "npm run build", "test": "npm run lint && prettier --check **/*.{ts,json,md}"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "2.3.0"}