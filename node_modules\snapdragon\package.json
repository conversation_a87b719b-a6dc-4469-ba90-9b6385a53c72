{"_from": "snapdragon@^0.8.1", "_id": "snapdragon@0.8.2", "_inBundle": false, "_integrity": "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==", "_location": "/snapdragon", "_phantomChildren": {"is-descriptor": "0.1.7", "is-extendable": "0.1.1"}, "_requested": {"type": "range", "registry": true, "raw": "snapdragon@^0.8.1", "name": "snapdragon", "escapedName": "snapdragon", "rawSpec": "^0.8.1", "saveSpec": null, "fetchSpec": "^0.8.1"}, "_requiredBy": ["/expand-brackets", "/extglob", "/fast-glob/braces", "/fast-glob/micromatch", "/nanomatch", "/watchpack-chokidar2/braces", "/watchpack-chokidar2/micromatch", "/webpack-dev-server/braces", "/webpack-dev-server/micromatch", "/webpack/braces", "/webpack/micromatch"], "_resolved": "http://*************:4873/snapdragon/-/snapdragon-0.8.2.tgz", "_shasum": "64922e7c565b0e14204ba1aa7d6964278d25182d", "_spec": "snapdragon@^0.8.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\fast-glob\\node_modules\\micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/snapdragon/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://edwardbetts.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "dependencies": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "deprecated": false, "description": "Fast, pluggable and easy-to-use parser-renderer factory.", "devDependencies": {"gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.10", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.0", "mocha": "^3.0.2"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "lib"], "homepage": "https://github.com/jonschlinkert/snapdragon", "keywords": ["lexer", "snapdragon"], "license": "MIT", "main": "index.js", "name": "snapdragon", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/snapdragon.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"description": "These libraries use snapdragon:", "list": ["braces", "expand-brackets", "extglob", "micromatch"]}, "reflinks": ["css", "pug", "verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "version": "0.8.2"}