{"_from": "express@^4.16.3", "_id": "express@4.21.2", "_inBundle": false, "_integrity": "sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA==", "_location": "/express", "_phantomChildren": {"side-channel": "1.1.0"}, "_requested": {"type": "range", "registry": true, "raw": "express@^4.16.3", "name": "express", "escapedName": "express", "rawSpec": "^4.16.3", "saveSpec": null, "fetchSpec": "^4.16.3"}, "_requiredBy": ["/webpack-bundle-analyzer", "/webpack-dev-server"], "_resolved": "http://192.168.3.141:4873/express/-/express-4.21.2.tgz", "_shasum": "cf250e48362174ead6cea4a566abef0162c1ec32", "_spec": "express@^4.16.3", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\webpack-bundle-analyzer", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/expressjs/express/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> <PERSON>", "email": "<EMAIL>"}], "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.12", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "deprecated": false, "description": "Fast, unopinionated, minimalist web framework", "devDependencies": {"after": "0.8.2", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "ejs": "3.1.9", "eslint": "8.47.0", "express-session": "1.17.2", "hbs": "4.2.0", "marked": "0.7.0", "method-override": "3.0.0", "mocha": "10.2.0", "morgan": "1.10.0", "nyc": "15.1.0", "pbkdf2-password": "1.2.1", "supertest": "6.3.0", "vhost": "~3.0.2"}, "engines": {"node": ">= 0.10.0"}, "files": ["LICENSE", "History.md", "Readme.md", "index.js", "lib/"], "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}, "homepage": "http://expressjs.com/", "keywords": ["express", "framework", "sinatra", "web", "http", "rest", "restful", "router", "app", "api"], "license": "MIT", "name": "express", "repository": {"type": "git", "url": "git+https://github.com/expressjs/express.git"}, "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --bail --check-leaks test/ test/acceptance/", "test-ci": "nyc --exclude examples --exclude test --exclude benchmarks --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --exclude examples --exclude test --exclude benchmarks --reporter=html --reporter=text npm test", "test-tap": "mocha --require test/support/env --reporter tap --check-leaks test/ test/acceptance/"}, "version": "4.21.2"}