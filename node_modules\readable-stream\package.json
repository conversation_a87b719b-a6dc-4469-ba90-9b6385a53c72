{"_from": "readable-stream@^2.2.2", "_id": "readable-stream@2.3.8", "_inBundle": false, "_integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "_location": "/readable-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "readable-stream@^2.2.2", "name": "readable-stream", "escapedName": "readable-stream", "rawSpec": "^2.2.2", "saveSpec": null, "fetchSpec": "^2.2.2"}, "_requiredBy": ["/browserify-sign", "/concat-stream", "/duplexify", "/enhanced-resolve/memory-fs", "/flush-write-stream", "/from2", "/fs-write-stream-atomic", "/hpack.js", "/memory-fs", "/node-libs-browser", "/parallel-transform", "/stream-browserify", "/stream-http", "/through2", "/watchpack-chokidar2/readdirp", "/webpack-dev-server/readdirp"], "_resolved": "http://192.168.3.141:4873/readable-stream/-/readable-stream-2.3.8.tgz", "_shasum": "91125e8042bba1b9887f49345f6277027ce8be9b", "_spec": "readable-stream@^2.2.2", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\concat-stream", "browser": {"util": false, "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./duplex.js": "./duplex-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "bundleDependencies": false, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}, "deprecated": false, "description": "Streams3, a user-land copy of the stream library from Node.js", "devDependencies": {"assert": "^1.4.0", "babel-polyfill": "^6.9.1", "buffer": "^4.9.0", "lolex": "^2.3.2", "nyc": "^6.4.0", "tap": "^0.7.0", "tape": "^4.8.0"}, "homepage": "https://github.com/nodejs/readable-stream#readme", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "main": "readable.js", "name": "readable-stream", "nyc": {"include": ["lib/**.js"]}, "repository": {"type": "git", "url": "git://github.com/nodejs/readable-stream.git"}, "scripts": {"ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js"}, "version": "2.3.8"}