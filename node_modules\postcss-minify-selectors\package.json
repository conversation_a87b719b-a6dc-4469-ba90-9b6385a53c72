{"_from": "postcss-minify-selectors@^4.0.2", "_id": "postcss-minify-selectors@4.0.2", "_inBundle": false, "_integrity": "sha512-D5S1iViljXBj9kflQo4YutWnJmwm8VvIsU1GeXJGiG9j8CIg9zs4voPMdQDUmIxetUOh60VilsNzCiAFTOqu3g==", "_location": "/postcss-minify-selectors", "_phantomChildren": {"dot-prop": "5.3.0", "indexes-of": "1.0.1", "source-map": "0.6.1", "uniq": "1.0.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-minify-selectors@^4.0.2", "name": "postcss-minify-selectors", "escapedName": "postcss-minify-selectors", "rawSpec": "^4.0.2", "saveSpec": null, "fetchSpec": "^4.0.2"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "http://192.168.3.141:4873/postcss-minify-selectors/-/postcss-minify-selectors-4.0.2.tgz", "_shasum": "e2e5eb40bfee500d0cd9243500f5f8ea4262fbd8", "_spec": "postcss-minify-selectors@^4.0.2", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cssnano-preset-default", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"alphanum-sort": "^1.0.0", "has": "^1.0.0", "postcss": "^7.0.0", "postcss-selector-parser": "^3.0.0"}, "deprecated": false, "description": "Minify selectors with PostCSS.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "postcss-font-magician": "^2.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["dist", "LICENSE-MIT"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["css", "minify", "optimise", "postcss", "postcss-plugin", "selectors"], "license": "MIT", "main": "dist/index.js", "name": "postcss-minify-selectors", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.2"}