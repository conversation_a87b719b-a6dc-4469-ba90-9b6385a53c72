{"_from": "postcss-loader@^3.0.0", "_id": "postcss-loader@3.0.0", "_inBundle": false, "_integrity": "sha512-cLWoDEY5OwHcAjDnkyRQzAXfs2jrKjXpO/HQFcc5b5u/r7aa471wdmChmwfnv7x2u840iat/wi0lQ5nbRgSkUA==", "_location": "/postcss-loader", "_phantomChildren": {"ajv": "6.12.6", "ajv-errors": "1.0.1", "ajv-keywords": "3.5.2", "source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-loader@^3.0.0", "name": "postcss-loader", "escapedName": "postcss-loader", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://192.168.3.141:4873/postcss-loader/-/postcss-loader-3.0.0.tgz", "_shasum": "6b97943e47c72d845fa9e03f273773d4e8dd6c2d", "_spec": "postcss-loader@^3.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/postcss/postcss-loader/issues"}, "bundleDependencies": false, "dependencies": {"loader-utils": "^1.1.0", "postcss": "^7.0.0", "postcss-load-config": "^2.0.0", "schema-utils": "^1.0.0"}, "deprecated": false, "description": "PostCSS loader for webpack", "devDependencies": {"@webpack-utilities/test": "^1.0.0-alpha.0", "jest": "^23.0.0", "jsdoc-to-markdown": "^4.0.0", "postcss-import": "^11.0.0", "postcss-js": "^2.0.0", "standard": "^11.0.0", "standard-version": "^4.0.0", "sugarss": "^1.0.0", "webpack": "^4.0.0"}, "engines": {"node": ">= 6"}, "files": ["src"], "homepage": "https://github.com/postcss/postcss-loader#readme", "keywords": ["css", "postcss", "postcss-runner", "webpack", "webpack-loader"], "license": "MIT", "main": "src/index.js", "name": "postcss-loader", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-loader.git"}, "scripts": {"clean": "rm -rf coverage test/outputs", "docs": "jsdoc2md src/*.js > docs/LOADER.md", "lint": "standard --env jest", "release": "standard-version", "test": "jest --env node --verbose --coverage"}, "version": "3.0.0"}