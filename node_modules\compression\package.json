{"_from": "compression@^1.7.4", "_id": "compression@1.8.1", "_inBundle": false, "_integrity": "sha512-9mAqGPHLakhCLeNyxPkK4xVo746zQ/czLH1Ky+vkitMnWfWZps8r0qXuwhwizagCRttsL4lfG4pIOvaWLpAP0w==", "_location": "/compression", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "compression@^1.7.4", "name": "compression", "escapedName": "compression", "rawSpec": "^1.7.4", "saveSpec": null, "fetchSpec": "^1.7.4"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "http://192.168.3.141:4873/compression/-/compression-1.8.1.tgz", "_shasum": "4a45d909ac16509195a9a28bd91094889c180d79", "_spec": "compression@^1.7.4", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\webpack-dev-server", "bugs": {"url": "https://github.com/expressjs/compression/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.1.0", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "deprecated": false, "description": "Node.js compression middleware", "devDependencies": {"after": "0.8.2", "eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.32.0", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0", "supertest": "6.3.4"}, "engines": {"node": ">= 0.8.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/compression#readme", "keywords": ["compression", "gzip", "deflate", "middleware", "express", "brotli", "http", "stream"], "license": "MIT", "name": "compression", "repository": {"type": "git", "url": "git+https://github.com/expressjs/compression.git"}, "scripts": {"lint": "eslint .", "test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "1.8.1"}