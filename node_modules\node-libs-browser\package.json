{"_from": "node-libs-browser@^2.2.1", "_id": "node-libs-browser@2.2.1", "_inBundle": false, "_integrity": "sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q==", "_location": "/node-libs-browser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "node-libs-browser@^2.2.1", "name": "node-libs-browser", "escapedName": "node-libs-browser", "rawSpec": "^2.2.1", "saveSpec": null, "fetchSpec": "^2.2.1"}, "_requiredBy": ["/webpack"], "_resolved": "http://192.168.3.141:4873/node-libs-browser/-/node-libs-browser-2.2.1.tgz", "_shasum": "b64f513d18338625f90346d27b0d235e631f6425", "_spec": "node-libs-browser@^2.2.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\webpack", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack/node-libs-browser/issues"}, "bundleDependencies": false, "dependencies": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^3.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.1", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.11.0", "vm-browserify": "^1.0.1"}, "deprecated": false, "description": "The node core libs for in browser usage.", "files": ["index.js", "mock/"], "homepage": "http://github.com/webpack/node-libs-browser", "license": "MIT", "main": "index.js", "name": "node-libs-browser", "repository": {"type": "git", "url": "git+https://github.com/webpack/node-libs-browser.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "2.2.1"}