{"_from": "cssnano-preset-default@^4.0.0", "_id": "cssnano-preset-default@4.0.8", "_inBundle": false, "_integrity": "sha512-LdAyHuq+VRyeVREFmuxUZR1TXjQm8QQU/ktoo/x7bz+SdOge1YKc5eMN6pRW7YWBmyq59CqYba1dJ5cUukEjLQ==", "_location": "/cssnano-preset-default", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "cssnano-preset-default@^4.0.0", "name": "cssnano-preset-default", "escapedName": "cssnano-preset-default", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/@intervolga/optimize-cssnano-plugin", "/cssnano"], "_resolved": "http://192.168.3.141:4873/cssnano-preset-default/-/cssnano-preset-default-4.0.8.tgz", "_shasum": "920622b1fc1e95a34e8838203f1397a504f2d3ff", "_spec": "cssnano-preset-default@^4.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@intervolga\\optimize-cssnano-plugin", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"css-declaration-sorter": "^4.0.1", "cssnano-util-raw-cache": "^4.0.1", "postcss": "^7.0.0", "postcss-calc": "^7.0.1", "postcss-colormin": "^4.0.3", "postcss-convert-values": "^4.0.1", "postcss-discard-comments": "^4.0.2", "postcss-discard-duplicates": "^4.0.2", "postcss-discard-empty": "^4.0.1", "postcss-discard-overridden": "^4.0.1", "postcss-merge-longhand": "^4.0.11", "postcss-merge-rules": "^4.0.3", "postcss-minify-font-values": "^4.0.2", "postcss-minify-gradients": "^4.0.2", "postcss-minify-params": "^4.0.2", "postcss-minify-selectors": "^4.0.2", "postcss-normalize-charset": "^4.0.1", "postcss-normalize-display-values": "^4.0.2", "postcss-normalize-positions": "^4.0.2", "postcss-normalize-repeat-style": "^4.0.2", "postcss-normalize-string": "^4.0.2", "postcss-normalize-timing-functions": "^4.0.2", "postcss-normalize-unicode": "^4.0.1", "postcss-normalize-url": "^4.0.1", "postcss-normalize-whitespace": "^4.0.2", "postcss-ordered-values": "^4.1.2", "postcss-reduce-initial": "^4.0.3", "postcss-reduce-transforms": "^4.0.2", "postcss-svgo": "^4.0.3", "postcss-unique-selectors": "^4.0.1"}, "deprecated": false, "description": "Safe defaults for cssnano which require minimal configuration.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["LICENSE-MIT", "dist"], "homepage": "https://github.com/cssnano/cssnano", "license": "MIT", "main": "dist/index.js", "name": "cssnano-preset-default", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.8"}