{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\test\\view\\src\\components\\MessageRenderer.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\src\\components\\MessageRenderer.vue", "mtime": 1754029686314}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBtYXJrZWQgZnJvbSAnbWFya2VkJwppbXBvcnQgaGxqcyBmcm9tICdoaWdobGlnaHQuanMnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ01lc3NhZ2VSZW5kZXJlcicsCiAgcHJvcHM6IHsKICAgIGNvbnRlbnQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIHJlbmRlcmVkQ29udGVudCgpIHsKICAgICAgcmV0dXJuIHRoaXMucmVuZGVyTWFya2Rvd24odGhpcy5jb250ZW50KQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgcmVuZGVyTWFya2Rvd24oY29udGVudCkgewogICAgICAvLyDphY3nva5tYXJrZWTpgInpobkKICAgICAgbWFya2VkLnNldE9wdGlvbnMoewogICAgICAgIGhpZ2hsaWdodDogZnVuY3Rpb24oY29kZSwgbGFuZ3VhZ2UpIHsKICAgICAgICAgIGlmIChsYW5ndWFnZSAmJiBobGpzLmdldExhbmd1YWdlKGxhbmd1YWdlKSkgewogICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgIHJldHVybiBobGpzLmhpZ2hsaWdodChsYW5ndWFnZSwgY29kZSkudmFsdWUKICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCfku6PnoIHpq5jkuq7lpLHotKU6JywgZXJyKQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICByZXR1cm4gaGxqcy5oaWdobGlnaHRBdXRvKGNvZGUpLnZhbHVlCiAgICAgICAgfSwKICAgICAgICBicmVha3M6IHRydWUsCiAgICAgICAgZ2ZtOiB0cnVlLAogICAgICAgIHRhYmxlczogdHJ1ZSwKICAgICAgICBzYW5pdGl6ZTogZmFsc2UKICAgICAgfSkKICAgICAgCiAgICAgIHJldHVybiBtYXJrZWQoY29udGVudCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["MessageRenderer.vue"], "names": [], "mappings": ";AAKA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "MessageRenderer.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <div class=\"message-renderer\" v-html=\"renderedContent\"></div>\n</template>\n\n<script>\nimport marked from 'marked'\nimport hljs from 'highlight.js'\n\nexport default {\n  name: 'MessageRenderer',\n  props: {\n    content: {\n      type: String,\n      required: true\n    }\n  },\n  computed: {\n    renderedContent() {\n      return this.renderMarkdown(this.content)\n    }\n  },\n  methods: {\n    renderMarkdown(content) {\n      // 配置marked选项\n      marked.setOptions({\n        highlight: function(code, language) {\n          if (language && hljs.getLanguage(language)) {\n            try {\n              return hljs.highlight(language, code).value\n            } catch (err) {\n              console.warn('代码高亮失败:', err)\n            }\n          }\n          return hljs.highlightAuto(code).value\n        },\n        breaks: true,\n        gfm: true,\n        tables: true,\n        sanitize: false\n      })\n      \n      return marked(content)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.message-renderer {\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 标题样式 */\n.message-renderer >>> h1 {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 16px 0 12px 0;\n  color: #2c3e50;\n  border-bottom: 2px solid #3498db;\n  padding-bottom: 8px;\n}\n\n.message-renderer >>> h2 {\n  font-size: 20px;\n  font-weight: bold;\n  margin: 14px 0 10px 0;\n  color: #34495e;\n  border-bottom: 1px solid #bdc3c7;\n  padding-bottom: 6px;\n}\n\n.message-renderer >>> h3 {\n  font-size: 18px;\n  font-weight: bold;\n  margin: 12px 0 8px 0;\n  color: #34495e;\n}\n\n.message-renderer >>> h4,\n.message-renderer >>> h5,\n.message-renderer >>> h6 {\n  font-size: 16px;\n  font-weight: bold;\n  margin: 10px 0 6px 0;\n  color: #34495e;\n}\n\n/* 段落样式 */\n.message-renderer >>> p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n/* 列表样式 */\n.message-renderer >>> ul,\n.message-renderer >>> ol {\n  margin: 8px 0;\n  padding-left: 24px;\n}\n\n.message-renderer >>> li {\n  margin: 4px 0;\n  line-height: 1.5;\n}\n\n/* 链接样式 */\n.message-renderer >>> a {\n  color: #3498db;\n  text-decoration: none;\n  border-bottom: 1px solid transparent;\n  transition: all 0.2s ease;\n}\n\n.message-renderer >>> a:hover {\n  color: #2980b9;\n  border-bottom-color: #2980b9;\n}\n\n/* 强调文本样式 */\n.message-renderer >>> strong {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.message-renderer >>> em {\n  font-style: italic;\n  color: #7f8c8d;\n}\n\n/* 行内代码样式 */\n.message-renderer >>> code {\n  background-color: #f8f9fa;\n  color: #e74c3c;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 0.9em;\n  border: 1px solid #e9ecef;\n}\n\n/* 代码块样式 */\n.message-renderer >>> pre {\n  background-color: #f8f9fa;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  padding: 16px;\n  margin: 12px 0;\n  overflow-x: auto;\n  position: relative;\n}\n\n.message-renderer >>> pre code {\n  background: none;\n  color: inherit;\n  padding: 0;\n  border: none;\n  border-radius: 0;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n/* 表格样式 */\n.message-renderer >>> table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 16px 0;\n  background-color: #fff;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.message-renderer >>> thead {\n  background-color: #f8f9fa;\n}\n\n.message-renderer >>> th {\n  padding: 12px 16px;\n  text-align: left;\n  font-weight: bold;\n  color: #2c3e50;\n  border-bottom: 2px solid #dee2e6;\n  font-size: 14px;\n}\n\n.message-renderer >>> td {\n  padding: 12px 16px;\n  border-bottom: 1px solid #dee2e6;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.message-renderer >>> tbody tr:hover {\n  background-color: #f8f9fa;\n}\n\n.message-renderer >>> tbody tr:last-child td {\n  border-bottom: none;\n}\n\n/* 引用块样式 */\n.message-renderer >>> blockquote {\n  border-left: 4px solid #3498db;\n  margin: 16px 0;\n  padding: 12px 16px;\n  background-color: #f8f9fa;\n  border-radius: 0 8px 8px 0;\n  color: #555;\n  font-style: italic;\n}\n\n.message-renderer >>> blockquote p {\n  margin: 0;\n}\n\n/* 分割线样式 */\n.message-renderer >>> hr {\n  border: none;\n  height: 2px;\n  background: linear-gradient(to right, transparent, #bdc3c7, transparent);\n  margin: 20px 0;\n}\n\n/* 图片样式 */\n.message-renderer >>> img {\n  max-width: 100%;\n  height: auto;\n  border-radius: 8px;\n  margin: 8px 0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n/* 复选框列表样式 */\n.message-renderer >>> input[type=\"checkbox\"] {\n  margin-right: 8px;\n  transform: scale(1.2);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .message-renderer >>> table {\n    font-size: 12px;\n  }\n  \n  .message-renderer >>> th,\n  .message-renderer >>> td {\n    padding: 8px 12px;\n  }\n  \n  .message-renderer >>> pre {\n    padding: 12px;\n    font-size: 12px;\n  }\n  \n  .message-renderer >>> h1 {\n    font-size: 20px;\n  }\n  \n  .message-renderer >>> h2 {\n    font-size: 18px;\n  }\n  \n  .message-renderer >>> h3 {\n    font-size: 16px;\n  }\n}\n</style>\n"]}]}