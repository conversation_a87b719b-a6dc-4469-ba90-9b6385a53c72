{"_from": "cli-highlight@^2.1.4", "_id": "cli-highlight@2.1.11", "_inBundle": false, "_integrity": "sha512-9KDcoEVwyUXrjcJNvHD0NFc/hiwe/WPVYIleQh2O1N2Zro5gWJZ/K+3DGn8w8P/F6FxOgzyC5bxDyHIgCSPhGg==", "_location": "/cli-highlight", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cli-highlight@^2.1.4", "name": "cli-highlight", "escapedName": "cli-highlight", "rawSpec": "^2.1.4", "saveSpec": null, "fetchSpec": "^2.1.4"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://192.168.3.141:4873/cli-highlight/-/cli-highlight-2.1.11.tgz", "_shasum": "49736fa452f0aaf4fae580e30acb26828d2dc1bf", "_spec": "cli-highlight@^2.1.4", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bin": {"highlight": "bin/highlight"}, "bugs": {"url": "https://github.com/felixfbecker/cli-highlight/issues"}, "bundleDependencies": false, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"chalk": "^4.0.0", "highlight.js": "^10.7.1", "mz": "^2.4.0", "parse5": "^5.1.1", "parse5-htmlparser2-tree-adapter": "^6.0.0", "yargs": "^16.0.0"}, "deprecated": false, "description": "Syntax highlighting in your terminal", "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@eclass/semantic-release-surge": "^1.0.7", "@sourcegraph/eslint-config": "^0.20.16", "@sourcegraph/prettierrc": "^3.0.3", "@types/jest": "^24.0.9", "@types/mz": "0.0.32", "@types/node": "^14.14.9", "@types/parse5": "^5.0.2", "@types/parse5-htmlparser2-tree-adapter": "^5.0.1", "@types/yargs": "^13.0.0", "eslint": "^7.14.0", "husky": "^3.0.0", "jest": "^24.1.0", "prettier": "^2.2.0", "semantic-release": "^17.2.4", "ts-jest": "^24.0.0", "typedoc": "^0.19.0", "typescript": "^4.1.2"}, "engines": {"node": ">=8.0.0", "npm": ">=5.0.0"}, "files": ["dist", "bin", "README.md", "LICENSE.txt"], "homepage": "https://github.com/felixfbecker/cli-highlight#readme", "husky": {"hooks": {"commit-msg": "commitlint -e $HUSKY_GIT_PARAMS"}}, "jest": {"collectCoverage": true, "transform": {"^.+\\.tsx?$": "ts-jest"}, "testRegex": "(/test/.*|/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "testPathIgnorePatterns": ["/node_modules/", "/dist/", "/src/test/__fixtures__/"], "coverageReporters": ["json", "text"], "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json"]}, "keywords": ["terminal", "syntax", "highlight", "color", "cli", "ansi"], "license": "ISC", "main": "dist/index.js", "name": "cli-highlight", "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", ["@eclass/semantic-release-surge", {"alias": "cli-highlight.surge.sh", "assets": "./typedoc/", "buildScriptName": "typedoc"}]]}, "repository": {"type": "git", "url": "git+https://github.com/felixfbecker/cli-highlight.git"}, "scripts": {"build": "tsc -p .", "eslint": "eslint 'src/**/*.ts'", "lint": "npm run eslint && npm run prettier", "prettier": "prettier --write --list-different '**/{*.ts,*.json,.prettierrc}'", "semantic-release": "semantic-release", "test": "jest", "typedoc": "typedoc --media media --mode file --excludeNotExported --out typedoc src/index.ts", "watch": "tsc -p . -w"}, "types": "dist/index.d.ts", "version": "2.1.11"}