{"_from": "terser-webpack-plugin@^1.4.4", "_id": "terser-webpack-plugin@1.4.6", "_inBundle": false, "_integrity": "sha512-2lBVf/VMVIddjSn3GqbT90GvIJ/eYXJkt8cTzU7NbjKqK8fwv18Ftr4PlbF46b/e88743iZFL5Dtr/rC4hjIeA==", "_location": "/terser-webpack-plugin", "_phantomChildren": {"ajv": "6.12.6", "ajv-errors": "1.0.1", "ajv-keywords": "3.5.2", "commondir": "1.0.1", "p-limit": "2.3.0", "pify": "4.0.1"}, "_requested": {"type": "range", "registry": true, "raw": "terser-webpack-plugin@^1.4.4", "name": "terser-webpack-plugin", "escapedName": "terser-webpack-plugin", "rawSpec": "^1.4.4", "saveSpec": null, "fetchSpec": "^1.4.4"}, "_requiredBy": ["/@vue/cli-service", "/webpack"], "_resolved": "http://192.168.3.141:4873/terser-webpack-plugin/-/terser-webpack-plugin-1.4.6.tgz", "_shasum": "87fcb6593fd1c977cd09e56143ecd31404600755", "_spec": "terser-webpack-plugin@^1.4.4", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "webpack Contrib Team"}, "bugs": {"url": "https://github.com/webpack-contrib/terser-webpack-plugin/issues"}, "bundleDependencies": false, "dependencies": {"cacache": "^12.0.2", "find-cache-dir": "^2.1.0", "is-wsl": "^1.1.0", "schema-utils": "^1.0.0", "serialize-javascript": "^4.0.0", "source-map": "^0.6.1", "terser": "^4.1.2", "webpack-sources": "^1.4.0", "worker-farm": "^1.7.0"}, "deprecated": false, "description": "Terser plugin for webpack", "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.8.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "del": "^4.1.1", "del-cli": "^1.1.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.18.2", "husky": "^3.0.2", "jest": "^24.8.0", "jest-junit": "^7.0.0", "lint-staged": "^9.2.1", "memory-fs": "^0.4.1", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "standard-version": "^7.0.0", "uglify-js": "^3.6.0", "webpack": "^4.38.0"}, "engines": {"node": ">= 6.9.0"}, "files": ["dist"], "homepage": "https://github.com/webpack-contrib/terser-webpack-plugin", "keywords": ["uglify", "uglify-js", "uglify-es", "terser", "webpack", "webpack-plugin", "minification", "compress", "compressor", "min", "minification", "minifier", "minify", "optimize", "optimizer"], "license": "MIT", "main": "dist/cjs.js", "name": "terser-webpack-plugin", "peerDependencies": {"webpack": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/terser-webpack-plugin.git"}, "scripts": {"build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "defaults": "webpack-defaults", "lint": "npm-run-all -l -p \"lint:**\"", "lint:js": "eslint --cache src test", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "prebuild": "npm run clean", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "security": "npm audit", "start": "npm run build -- -w", "test": "cross-env NODE_ENV=test npm run test:coverage", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch"}, "version": "1.4.6"}