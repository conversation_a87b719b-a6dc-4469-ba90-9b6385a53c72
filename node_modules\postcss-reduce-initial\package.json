{"_from": "postcss-reduce-initial@^4.0.3", "_id": "postcss-reduce-initial@4.0.3", "_inBundle": false, "_integrity": "sha512-gKWmR5aUulSjbzOfD9AlJiHCGH6AEVLaM0AV+aSioxUDd16qXP1PCh8d1/BGVvpdWn8k/HiK7n6TjeoXN1F7DA==", "_location": "/postcss-reduce-initial", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-reduce-initial@^4.0.3", "name": "postcss-reduce-initial", "escapedName": "postcss-reduce-initial", "rawSpec": "^4.0.3", "saveSpec": null, "fetchSpec": "^4.0.3"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "http://192.168.3.141:4873/postcss-reduce-initial/-/postcss-reduce-initial-4.0.3.tgz", "_shasum": "7fd42ebea5e9c814609639e2c2e84ae270ba48df", "_spec": "postcss-reduce-initial@^4.0.3", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cssnano-preset-default", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"browserslist": "^4.0.0", "caniuse-api": "^3.0.0", "has": "^1.0.0", "postcss": "^7.0.0"}, "deprecated": false, "description": "Reduce initial definitions to the actual initial value, where possible.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "got": "^8.0.0", "html2plaintext": "^2.0.0", "write-file": "^1.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["data", "dist/index.js", "LICENSE-MIT"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-reduce-initial", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"acquire": "babel-node ./src/acquire.js", "prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/,src/acquire.js"}, "version": "4.0.3"}