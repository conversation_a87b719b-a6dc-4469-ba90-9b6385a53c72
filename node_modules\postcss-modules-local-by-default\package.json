{"_from": "postcss-modules-local-by-default@^3.0.2", "_id": "postcss-modules-local-by-default@3.0.3", "_inBundle": false, "_integrity": "sha512-e3xDq+LotiGesympRlKNgaJ0PCzoUIdpH0dj47iWAui/kyTgh3CiAr1qP54uodmJhl6p9rN6BoNcdEDVJx9RDw==", "_location": "/postcss-modules-local-by-default", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-modules-local-by-default@^3.0.2", "name": "postcss-modules-local-by-default", "escapedName": "postcss-modules-local-by-default", "rawSpec": "^3.0.2", "saveSpec": null, "fetchSpec": "^3.0.2"}, "_requiredBy": ["/css-loader"], "_resolved": "http://192.168.3.141:4873/postcss-modules-local-by-default/-/postcss-modules-local-by-default-3.0.3.tgz", "_shasum": "bb14e0cc78279d504dbdcbfd7e0ca28993ffbbb0", "_spec": "postcss-modules-local-by-default@^3.0.2", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\css-loader", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/css-modules/postcss-modules-local-by-default/issues"}, "bundleDependencies": false, "dependencies": {"icss-utils": "^4.1.1", "postcss": "^7.0.32", "postcss-selector-parser": "^6.0.2", "postcss-value-parser": "^4.1.0"}, "deprecated": false, "description": "A CSS Modules transform to make local scope the default", "devDependencies": {"chokidar-cli": "^1.2.3", "codecov.io": "^0.1.6", "coveralls": "^3.1.0", "eslint": "^5.16.0", "istanbul": "^0.4.5", "tape": "^5.0.1"}, "engines": {"node": ">= 6"}, "files": ["index.js"], "homepage": "https://github.com/css-modules/postcss-modules-local-by-default#readme", "keywords": ["css-modules", "postcss", "css", "postcss-plugin"], "license": "MIT", "main": "index.js", "name": "postcss-modules-local-by-default", "prettier": {"singleQuote": true, "trailingComma": "es5"}, "repository": {"type": "git", "url": "git+https://github.com/css-modules/postcss-modules-local-by-default.git"}, "scripts": {"autotest": "chokidar index.js test.js -c 'yarn test'", "cover": "istanbul cover test.js", "lint": "eslint index.js test.js", "precover": "yarn lint", "prepublish": "yarn test", "pretest": "yarn lint", "test": "tape test.js", "travis": "yarn lint && yarn cover -- --report lcovonly"}, "version": "3.0.3"}