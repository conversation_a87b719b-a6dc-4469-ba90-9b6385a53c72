{"_from": "read-pkg@^5.1.1", "_id": "read-pkg@5.2.0", "_inBundle": false, "_integrity": "sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==", "_location": "/read-pkg", "_phantomChildren": {"@babel/code-frame": "7.27.1", "error-ex": "1.3.2", "json-parse-even-better-errors": "2.3.1", "lines-and-columns": "1.2.4"}, "_requested": {"type": "range", "registry": true, "raw": "read-pkg@^5.1.1", "name": "read-pkg", "escapedName": "read-pkg", "rawSpec": "^5.1.1", "saveSpec": null, "fetchSpec": "^5.1.1"}, "_requiredBy": ["/@vue/cli-shared-utils"], "_resolved": "http://192.168.3.141:4873/read-pkg/-/read-pkg-5.2.0.tgz", "_shasum": "7bf295438ca5a33e56cd30e053b34ee7250c93cc", "_spec": "read-pkg@^5.1.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-shared-utils", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/read-pkg/issues"}, "bundleDependencies": false, "dependencies": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "deprecated": false, "description": "Read a package.json file", "devDependencies": {"ava": "^2.2.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/read-pkg#readme", "keywords": ["json", "read", "parse", "file", "fs", "graceful", "load", "package", "normalize"], "license": "MIT", "name": "read-pkg", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/read-pkg.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "5.2.0", "xo": {"ignores": ["test/test.js"]}}