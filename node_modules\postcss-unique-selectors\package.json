{"_from": "postcss-unique-selectors@^4.0.1", "_id": "postcss-unique-selectors@4.0.1", "_inBundle": false, "_integrity": "sha512-+<PERSON><PERSON><PERSON><PERSON>o9QwZjKrmJgkI4Fn8SBgRO6WXQBJi7KiAVPlmxikB5Jzc4EvXMT2H0/m0RjrVVm9rGNhZddm/8Spg==", "_location": "/postcss-unique-selectors", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-unique-selectors@^4.0.1", "name": "postcss-unique-selectors", "escapedName": "postcss-unique-selectors", "rawSpec": "^4.0.1", "saveSpec": null, "fetchSpec": "^4.0.1"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "http://192.168.3.141:4873/postcss-unique-selectors/-/postcss-unique-selectors-4.0.1.tgz", "_shasum": "9446911f3289bfd64c6d680f073c03b1f9ee4bac", "_spec": "postcss-unique-selectors@^4.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cssnano-preset-default", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"alphanum-sort": "^1.0.0", "postcss": "^7.0.0", "uniqs": "^2.0.0"}, "deprecated": false, "description": "Ensure CSS selectors are unique.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["LICENSE-MIT", "dist"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-unique-selectors", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.1"}