{"_from": "finalhandler@1.3.1", "_id": "finalhandler@1.3.1", "_inBundle": false, "_integrity": "sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==", "_location": "/finalhandler", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "finalhandler@1.3.1", "name": "finalhandler", "escapedName": "finalhandler", "rawSpec": "1.3.1", "saveSpec": null, "fetchSpec": "1.3.1"}, "_requiredBy": ["/express"], "_resolved": "http://192.168.3.141:4873/finalhandler/-/finalhandler-1.3.1.tgz", "_shasum": "0c575f1d1d324ddd1da35ad7ece3df7d19088019", "_spec": "finalhandler@1.3.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "bundleDependencies": false, "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "deprecated": false, "description": "Node.js final http responder", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.26.0", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "10.0.0", "nyc": "15.1.0", "readable-stream": "2.3.6", "safe-buffer": "5.2.1", "supertest": "6.2.4"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "SECURITY.md", "index.js"], "homepage": "https://github.com/pillarjs/finalhandler#readme", "license": "MIT", "name": "finalhandler", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/finalhandler.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-inspect": "mocha --reporter spec --inspect --inspect-brk test/"}, "version": "1.3.1"}