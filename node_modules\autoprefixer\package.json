{"_from": "autoprefixer@^9.8.6", "_id": "autoprefixer@9.8.8", "_inBundle": false, "_integrity": "sha512-eM9d/swFopRt5gdJ7jrpCwgvEMIayITpojhkkSMRsFHYuH5bkSQ4p/9qTEHtmNudUZh22Tehu7I6CxAW0IXTKA==", "_location": "/autoprefixer", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "autoprefixer@^9.8.6", "name": "autoprefixer", "escapedName": "autoprefixer", "rawSpec": "^9.8.6", "saveSpec": null, "fetchSpec": "^9.8.6"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://192.168.3.141:4873/autoprefixer/-/autoprefixer-9.8.8.tgz", "_shasum": "fd4bd4595385fa6f06599de749a4d5f7a474957a", "_spec": "autoprefixer@^9.8.6", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "bugs": {"url": "https://github.com/postcss/autoprefixer/issues"}, "bundleDependencies": false, "dependencies": {"browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001109", "normalize-range": "^0.1.2", "num2fraction": "^1.2.2", "picocolors": "^0.2.1", "postcss": "^7.0.32", "postcss-value-parser": "^4.1.0"}, "deprecated": false, "description": "Parse CSS and add vendor prefixes to CSS rules using values from the Can I Use website", "funding": {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}, "homepage": "https://github.com/postcss/autoprefixer#readme", "keywords": ["autoprefixer", "css", "prefix", "postcss", "postcss-plugin"], "license": "MIT", "main": "lib/autoprefixer", "name": "autoprefixer", "repository": {"type": "git", "url": "git+https://github.com/postcss/autoprefixer.git"}, "version": "9.8.8"}