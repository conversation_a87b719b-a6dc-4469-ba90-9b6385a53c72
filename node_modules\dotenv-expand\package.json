{"_from": "dotenv-expand@^5.1.0", "_id": "dotenv-expand@5.1.0", "_inBundle": false, "_integrity": "sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==", "_location": "/dotenv-expand", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dotenv-expand@^5.1.0", "name": "dotenv-expand", "escapedName": "dotenv-expand", "rawSpec": "^5.1.0", "saveSpec": null, "fetchSpec": "^5.1.0"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://*************:4873/dotenv-expand/-/dotenv-expand-5.1.0.tgz", "_shasum": "3fbaf020bfd794884072ea26b1e9791d45a629f0", "_spec": "dotenv-expand@^5.1.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "motdotla"}, "bundleDependencies": false, "deprecated": false, "description": "Expand environment variables using dotenv", "devDependencies": {"dotenv": "^4.0.0", "lab": "^13.0.1", "should": "^11.2.1", "standard": "^9.0.2"}, "keywords": ["dotenv", "expand", "variables"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/main.js", "name": "dotenv-expand", "scripts": {"lint": "standard", "posttest": "npm run lint", "test": "lab test/* --coverage"}, "types": "./index.d.ts", "version": "5.1.0"}