{"_from": "copy-webpack-plugin@^5.1.1", "_id": "copy-webpack-plugin@5.1.2", "_inBundle": false, "_integrity": "sha512-Uh7crJAco3AjBvgAy9Z75CjK8IG+gxaErro71THQ+vv/bl4HaQcpkexAY8KVW/T6D2W2IRr+couF/knIRkZMIQ==", "_location": "/copy-webpack-plugin", "_phantomChildren": {"ajv": "6.12.6", "ajv-errors": "1.0.1", "ajv-keywords": "3.5.2", "array-union": "1.0.2", "commondir": "1.0.1", "dir-glob": "2.2.2", "glob": "7.2.3", "ignore": "3.3.10", "p-limit": "2.3.0", "pify": "4.0.1", "slash": "1.0.0"}, "_requested": {"type": "range", "registry": true, "raw": "copy-webpack-plugin@^5.1.1", "name": "copy-webpack-plugin", "escapedName": "copy-webpack-plugin", "rawSpec": "^5.1.1", "saveSpec": null, "fetchSpec": "^5.1.1"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://192.168.3.141:4873/copy-webpack-plugin/-/copy-webpack-plugin-5.1.2.tgz", "_shasum": "8a889e1dcafa6c91c6cd4be1ad158f1d3823bae2", "_spec": "copy-webpack-plugin@^5.1.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/webpack-contrib/copy-webpack-plugin/issues"}, "bundleDependencies": false, "dependencies": {"cacache": "^12.0.3", "find-cache-dir": "^2.1.0", "glob-parent": "^3.1.0", "globby": "^7.1.1", "is-glob": "^4.0.1", "loader-utils": "^1.2.3", "minimatch": "^3.0.4", "normalize-path": "^3.0.0", "p-limit": "^2.2.1", "schema-utils": "^1.0.0", "serialize-javascript": "^4.0.0", "webpack-log": "^2.0.0"}, "deprecated": false, "description": "Copy files && directories with webpack", "devDependencies": {"@babel/cli": "^7.7.5", "@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.6", "@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/defaults": "^6.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.1", "del": "^4.1.1", "del-cli": "^1.1.0", "enhanced-resolve": "^4.1.1", "eslint": "^6.7.2", "eslint-config-prettier": "^6.7.0", "eslint-plugin-import": "^2.19.1", "husky": "^3.1.0", "is-gzip": "^2.0.0", "jest": "^24.9.0", "jest-junit": "^10.0.0", "lint-staged": "^9.5.0", "memfs": "^3.0.1", "mkdirp": "^0.5.1", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.0.1", "webpack": "^4.41.2"}, "engines": {"node": ">= 6.9.0"}, "files": ["dist"], "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "homepage": "https://github.com/webpack-contrib/copy-webpack-plugin", "keywords": ["webpack", "plugin", "transfer", "move", "copy"], "license": "MIT", "main": "dist/cjs.js", "name": "copy-webpack-plugin", "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/copy-webpack-plugin.git"}, "scripts": {"build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "defaults": "webpack-defaults", "lint": "npm-run-all -l -p \"lint:**\"", "lint:js": "eslint --cache .", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "prebuild": "npm run clean", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "security": "npm audit", "start": "npm run build -- -w", "test": "npm run test:coverage", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch"}, "version": "5.1.2"}