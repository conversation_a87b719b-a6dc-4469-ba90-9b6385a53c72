{"_from": "vue-template-compiler@^2.6.10", "_id": "vue-template-compiler@2.7.16", "_inBundle": false, "_integrity": "sha512-AYbUWAJHLGGQM7+cNTELw+KsOG9nl2CnSv467WobS5Cv9uk3wFcnr1Etsz2sEIHEZvw1U+o9mRlEO6QbZvUPGQ==", "_location": "/vue-template-compiler", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vue-template-compiler@^2.6.10", "name": "vue-template-compiler", "escapedName": "vue-template-compiler", "rawSpec": "^2.6.10", "saveSpec": null, "fetchSpec": "^2.6.10"}, "_requiredBy": ["#DEV:/"], "_resolved": "http://*************:4873/vue-template-compiler/-/vue-template-compiler-2.7.16.tgz", "_shasum": "c81b2d47753264c77ac03b9966a46637482bb03b", "_spec": "vue-template-compiler@^2.6.10", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view", "author": {"name": "<PERSON>"}, "browser": "browser.js", "bugs": {"url": "https://github.com/vuejs/vue/issues"}, "bundleDependencies": false, "dependencies": {"de-indent": "^1.0.2", "he": "^1.2.0"}, "deprecated": false, "description": "template compiler for Vue 2.0", "devDependencies": {"vue": "file:../.."}, "files": ["types/*.d.ts", "*.js"], "homepage": "https://github.com/vuejs/vue/tree/dev/packages/vue-template-compiler#readme", "jsdelivr": "browser.js", "keywords": ["vue", "compiler"], "license": "MIT", "main": "index.js", "name": "vue-template-compiler", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue.git"}, "types": "types/index.d.ts", "unpkg": "browser.js", "version": "2.7.16"}