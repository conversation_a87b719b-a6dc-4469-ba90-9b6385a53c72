{"_from": "public-encrypt@^4.0.3", "_id": "public-encrypt@4.0.3", "_inBundle": false, "_integrity": "sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q==", "_location": "/public-encrypt", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "public-encrypt@^4.0.3", "name": "public-encrypt", "escapedName": "public-encrypt", "rawSpec": "^4.0.3", "saveSpec": null, "fetchSpec": "^4.0.3"}, "_requiredBy": ["/crypto-browserify"], "_resolved": "http://192.168.3.141:4873/public-encrypt/-/public-encrypt-4.0.3.tgz", "_shasum": "4fcc9d77a07e48ba7527e7cbe0de33d0701331e0", "_spec": "public-encrypt@^4.0.3", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\crypto-browserify", "author": {"name": "<PERSON>"}, "browser": "browser.js", "bugs": {"url": "https://github.com/crypto-browserify/publicEncrypt/issues"}, "bundleDependencies": false, "dependencies": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}, "deprecated": false, "description": "browserify version of publicEncrypt & privateDecrypt", "devDependencies": {"standard": "^12.0.0", "tap-spec": "^2.1.2", "tape": "^3.0.3"}, "directories": {"test": "test"}, "homepage": "https://github.com/crypto-browserify/publicEncrypt", "license": "MIT", "main": "index.js", "name": "public-encrypt", "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/publicEncrypt.git"}, "scripts": {"lint": "standard", "test": "node test/index.js | tspec"}, "version": "4.0.3"}