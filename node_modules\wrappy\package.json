{"_from": "wrappy@1", "_id": "wrappy@1.0.2", "_inBundle": false, "_integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "_location": "/wrappy", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "wrappy@1", "name": "wrappy", "escapedName": "wrappy", "rawSpec": "1", "saveSpec": null, "fetchSpec": "1"}, "_requiredBy": ["/inflight", "/once"], "_resolved": "http://*************:4873/wrappy/-/wrappy-1.0.2.tgz", "_shasum": "b5243d8f3ec1aa35f1364605bc0d1036e30ab69f", "_spec": "wrappy@1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\once", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/npm/wrappy/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Callback wrapping utility", "devDependencies": {"tap": "^2.3.1"}, "directories": {"test": "test"}, "files": ["wrappy.js"], "homepage": "https://github.com/npm/wrappy", "license": "ISC", "main": "wrappy.js", "name": "wrappy", "repository": {"type": "git", "url": "git+https://github.com/npm/wrappy.git"}, "scripts": {"test": "tap --coverage test/*.js"}, "version": "1.0.2"}