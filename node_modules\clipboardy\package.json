{"_from": "clipboardy@^2.3.0", "_id": "clipboardy@2.3.0", "_inBundle": false, "_integrity": "sha512-mKhiIL2DrQIsuXMgBgnfEHOZOryC7kY7YO//TN6c63wlEm3NG5tz+YgY5rVi29KCmq/QQjKYvM7a19+MDOTHOQ==", "_location": "/clipboardy", "_phantomChildren": {"is-docker": "2.2.1"}, "_requested": {"type": "range", "registry": true, "raw": "clipboardy@^2.3.0", "name": "clipboardy", "escapedName": "clipboardy", "rawSpec": "^2.3.0", "saveSpec": null, "fetchSpec": "^2.3.0"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://*************:4873/clipboardy/-/clipboardy-2.3.0.tgz", "_shasum": "3c2903650c68e46a91b388985bc2774287dba290", "_spec": "clipboardy@^2.3.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "browser": "browser.js", "bugs": {"url": "https://github.com/sindresorhus/clipboardy/issues"}, "bundleDependencies": false, "dependencies": {"arch": "^2.1.1", "execa": "^1.0.0", "is-wsl": "^2.1.1"}, "deprecated": false, "description": "Access the system clipboard (copy/paste)", "devDependencies": {"ava": "^2.1.0", "tsd": "^0.10.0", "xo": "^0.25.3"}, "engines": {"node": ">=8"}, "exports": {"browser": "./browser.js", "default": "./index.js"}, "files": ["index.js", "index.d.ts", "browser.js", "lib", "fallbacks"], "homepage": "https://github.com/sindresorhus/clipboardy#readme", "keywords": ["clipboard", "copy", "paste", "copy-paste", "pasteboard", "read", "write", "pbcopy", "clip", "xclip", "xsel"], "license": "MIT", "name": "clipboardy", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/clipboardy.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.3.0"}