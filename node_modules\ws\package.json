{"_from": "ws@^6.0.0", "_id": "ws@6.2.3", "_inBundle": false, "_integrity": "sha512-jmTjYU0j60B+vHey6TfR3Z7RD61z/hmxBS3VMSGIrroOWXQEneK1zNuotOUrGyBHQj0yrpsLHPWtigEFd13ndA==", "_location": "/ws", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ws@^6.0.0", "name": "ws", "escapedName": "ws", "rawSpec": "^6.0.0", "saveSpec": null, "fetchSpec": "^6.0.0"}, "_requiredBy": ["/webpack-bundle-analyzer", "/webpack-dev-server"], "_resolved": "http://*************:4873/ws/-/ws-6.2.3.tgz", "_shasum": "ccc96e4add5fd6fedbc491903075c85c5a11d9ee", "_spec": "ws@^6.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\webpack-bundle-analyzer", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "browser": "browser.js", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "bundleDependencies": false, "dependencies": {"async-limiter": "~1.0.0"}, "deprecated": false, "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "devDependencies": {"benchmark": "~2.1.4", "bufferutil": "~4.0.0", "coveralls": "~3.0.3", "eslint": "~5.15.0", "eslint-config-prettier": "~4.1.0", "eslint-plugin-prettier": "~3.0.0", "mocha": "~6.0.0", "nyc": "~13.3.0", "prettier": "~1.16.1", "utf-8-validate": "~5.0.0"}, "files": ["browser.js", "index.js", "lib/*.js"], "homepage": "https://github.com/websockets/ws", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "license": "MIT", "main": "index.js", "name": "ws", "repository": {"type": "git", "url": "git+https://github.com/websockets/ws.git"}, "scripts": {"integration": "npm run lint && mocha test/*.integration.js", "lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha test/*.test.js"}, "version": "6.2.3"}