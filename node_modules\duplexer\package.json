{"_from": "duplexer@^0.1.1", "_id": "duplexer@0.1.2", "_inBundle": false, "_integrity": "sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==", "_location": "/duplexer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "duplexer@^0.1.1", "name": "duplexer", "escapedName": "duplexer", "rawSpec": "^0.1.1", "saveSpec": null, "fetchSpec": "^0.1.1"}, "_requiredBy": ["/gzip-size"], "_resolved": "http://*************:4873/duplexer/-/duplexer-0.1.2.tgz", "_shasum": "3abe43aef3835f8ae077d136ddce0f276b0400e6", "_spec": "duplexer@^0.1.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\gzip-size", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Raynos/duplexer/issues", "email": "<EMAIL>"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>"}], "deprecated": false, "description": "Creates a duplex stream", "devDependencies": {"tape": "0.3.3", "through": "~0.1.4"}, "homepage": "https://github.com/Raynos/duplexer", "keywords": [], "license": "MIT", "main": "index", "name": "duplexer", "repository": {"type": "git", "url": "git://github.com/Raynos/duplexer.git"}, "scripts": {"test": "node test"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "version": "0.1.2"}