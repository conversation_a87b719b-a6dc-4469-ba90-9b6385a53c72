{"_from": "postcss-normalize-unicode@^4.0.1", "_id": "postcss-normalize-unicode@4.0.1", "_inBundle": false, "_integrity": "sha512-od18Uq2wCYn+vZ/qCOeutvHjB5jm57ToxRaMeNuf0nWVHaP9Hua56QyMF6fs/4FSUnVIw0CBPsU0K4LnBPwYwg==", "_location": "/postcss-normalize-unicode", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-normalize-unicode@^4.0.1", "name": "postcss-normalize-unicode", "escapedName": "postcss-normalize-unicode", "rawSpec": "^4.0.1", "saveSpec": null, "fetchSpec": "^4.0.1"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "http://192.168.3.141:4873/postcss-normalize-unicode/-/postcss-normalize-unicode-4.0.1.tgz", "_shasum": "841bd48fdcf3019ad4baa7493a3d363b52ae1cfb", "_spec": "postcss-normalize-unicode@^4.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cssnano-preset-default", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"browserslist": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "deprecated": false, "description": "Normalize unicode-range descriptors, and can convert to wildcard ranges.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["dist", "LICENSE-MIT"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-normalize-unicode", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.1"}