{"_from": "fast-glob@^2.2.6", "_id": "fast-glob@2.2.7", "_inBundle": false, "_integrity": "sha512-g1KuQwHOZAmOZMuBtHdxDtju+T2RT8jgCC9aANsbpdiDDTSnjgfuVsIBNKbUeJI3oKMRExcfNDtJl4OhbffMsw==", "_location": "/fast-glob", "_phantomChildren": {"arr-diff": "4.0.0", "arr-flatten": "1.1.0", "array-unique": "0.3.2", "define-property": "2.0.2", "extend-shallow": "3.0.2", "extglob": "2.0.4", "fragment-cache": "0.2.1", "is-buffer": "1.1.6", "is-extendable": "0.1.1", "isobject": "3.0.1", "kind-of": "6.0.3", "nanomatch": "1.2.13", "object.pick": "1.3.0", "regex-not": "1.0.2", "repeat-element": "1.1.4", "repeat-string": "1.6.1", "snapdragon": "0.8.2", "snapdragon-node": "2.1.1", "split-string": "3.1.0", "to-regex": "3.0.2"}, "_requested": {"type": "range", "registry": true, "raw": "fast-glob@^2.2.6", "name": "fast-glob", "escapedName": "fast-glob", "rawSpec": "^2.2.6", "saveSpec": null, "fetchSpec": "^2.2.6"}, "_requiredBy": ["/globby"], "_resolved": "http://192.168.3.141:4873/fast-glob/-/fast-glob-2.2.7.tgz", "_shasum": "6953857c3afa475fff92ee6015d52da70a4cd39d", "_spec": "fast-glob@^2.2.6", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\globby", "author": {"name": "<PERSON>", "url": "canonium.com"}, "bugs": {"url": "https://github.com/mrmlnc/fast-glob/issues"}, "bundleDependencies": false, "dependencies": {"@mrmlnc/readdir-enhanced": "^2.2.1", "@nodelib/fs.stat": "^1.1.2", "glob-parent": "^3.1.0", "is-glob": "^4.0.0", "merge2": "^1.2.3", "micromatch": "^3.1.10"}, "deprecated": false, "description": "Is a faster `node-glob` alternative", "devDependencies": {"@types/bash-glob": "^2.0.0", "@types/compute-stdev": "^1.0.0", "@types/easy-table": "^0.0.32", "@types/execa": "^0.9.0", "@types/glob": "^7.1.1", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/globby": "^8.0.0", "@types/is-glob": "^4.0.0", "@types/merge2": "^1.1.4", "@types/micromatch": "^3.1.0", "@types/minimist": "^1.2.0", "@types/mocha": "^5.2.5", "@types/node": "^11.13.5", "@types/rimraf": "^2.0.2", "bash-glob": "^2.0.0", "compute-stdev": "^1.0.0", "easy-table": "^1.1.1", "execa": "^0.9.0", "fast-glob": "^2.2.0", "glob": "^7.1.2", "glob-stream": "^6.1.0", "globby": "^8.0.1", "minimist": "^1.2.0", "mocha": "^5.2.0", "rimraf": "^2.6.2", "tiny-glob": "^0.2.3", "tslint": "^5.12.0", "tslint-config-mrmlnc": "^2.0.1", "typescript": "^3.1.3"}, "engines": {"node": ">=4.0.0"}, "homepage": "https://github.com/mrmlnc/fast-glob#readme", "keywords": ["glob", "patterns", "fast", "implementation"], "license": "MIT", "main": "index.js", "name": "fast-glob", "repository": {"type": "git", "url": "git+https://github.com/mrmlnc/fast-glob.git"}, "scripts": {"bench": "npm run build && npm run bench-async && npm run bench-sync", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-100": "node ./out/benchmark --depth 100", "bench-async-5": "node ./out/benchmark --depth 5", "bench-async-50": "node ./out/benchmark --depth 50", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "build": "npm run clean && npm run compile && npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> out", "compile": "tsc", "lint": "tslint \"src/**/*.ts\" -p . -t stylish", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "test": "mocha \"out/**/*.spec.js\" -s 0", "watch": "npm run clean && npm run compile -- --sourceMap --watch"}, "typings": "index.d.ts", "version": "2.2.7"}