{"_from": "cacache@^12.0.3", "_id": "cacache@12.0.4", "_inBundle": false, "_integrity": "sha512-a0tMB40oefvuInr4Cwb3GerbL9xTj1D5yg0T5xrjGCGyfvbxseIXX7BAO/u/hIXdafzOI5JC3wDwHyf24buOAQ==", "_location": "/cacache", "_phantomChildren": {"figgy-pudding": "3.5.2"}, "_requested": {"type": "range", "registry": true, "raw": "cacache@^12.0.3", "name": "cacache", "escapedName": "cacache", "rawSpec": "^12.0.3", "saveSpec": null, "fetchSpec": "^12.0.3"}, "_requiredBy": ["/copy-webpack-plugin", "/terser-webpack-plugin"], "_resolved": "http://192.168.3.141:4873/cacache/-/cacache-12.0.4.tgz", "_shasum": "668bcbd105aeb5f1d92fe25570ec9525c8faa40c", "_spec": "cacache@^12.0.3", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\copy-webpack-plugin", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/npm/cacache/issues"}, "bundleDependencies": false, "cache-version": {"content": "2", "index": "5"}, "config": {"nyc": {"exclude": ["node_modules/**", "test/**"]}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"bluebird": "^3.5.5", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.4", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.3", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}, "deprecated": false, "description": "Fast, fault-tolerant, cross-platform, disk-based, data-agnostic, content-addressable cache.", "devDependencies": {"benchmark": "^2.1.4", "chalk": "^2.4.2", "cross-env": "^5.1.4", "require-inject": "^1.4.4", "standard": "^12.0.1", "standard-version": "^6.0.1", "tacks": "^1.3.0", "tap": "^12.7.0"}, "files": ["*.js", "lib", "locales"], "homepage": "https://github.com/npm/cacache#readme", "keywords": ["cache", "caching", "content-addressable", "sri", "sri hash", "subresource integrity", "cache", "storage", "store", "file store", "filesystem", "disk cache", "disk storage"], "license": "ISC", "main": "index.js", "name": "cacache", "publishConfig": {"tag": "legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cacache.git"}, "scripts": {"benchmarks": "node test/benchmarks", "postrelease": "npm publish && git push --follow-tags", "prerelease": "npm t", "pretest": "standard", "release": "standard-version -s", "test": "cross-env CACACHE_UPDATE_LOCALE_FILES=true tap --coverage --nyc-arg=--all -J test/*.js", "test-docker": "docker run -it --rm --name pacotest -v \"$PWD\":/tmp -w /tmp node:latest npm test"}, "version": "12.0.4"}