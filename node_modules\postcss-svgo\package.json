{"_from": "postcss-svgo@^4.0.3", "_id": "postcss-svgo@4.0.3", "_inBundle": false, "_integrity": "sha512-NoRbrcMWTtUghzuKSoIm6XV+sJdvZ7GZSc3wdBN0W19FTtp2ko8NqLsgoh/m9CzNhU3KLPvQmjIwtaNFkaFTvw==", "_location": "/postcss-svgo", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-svgo@^4.0.3", "name": "postcss-svgo", "escapedName": "postcss-svgo", "rawSpec": "^4.0.3", "saveSpec": null, "fetchSpec": "^4.0.3"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "http://192.168.3.141:4873/postcss-svgo/-/postcss-svgo-4.0.3.tgz", "_shasum": "343a2cdbac9505d416243d496f724f38894c941e", "_spec": "postcss-svgo@^4.0.3", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cssnano-preset-default", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"postcss": "^7.0.0", "postcss-value-parser": "^3.0.0", "svgo": "^1.0.0"}, "deprecated": false, "description": "Optimise inline SVG with PostCSS.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "pleeease-filters": "^4.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["LICENSE-MIT", "dist"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["css", "minify", "optimise", "postcss", "postcss-plugin", "svg", "svgo"], "license": "MIT", "main": "dist/index.js", "name": "postcss-svgo", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.3"}