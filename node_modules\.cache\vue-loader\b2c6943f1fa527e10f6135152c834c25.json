{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\test\\view\\src\\components\\MessageRenderer.vue?vue&type=style&index=0&id=014bf2ea&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\src\\components\\MessageRenderer.vue", "mtime": 1754029686314}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MessageRenderer.vue"], "names": [], "mappings": ";AAgDA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "MessageRenderer.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <div class=\"message-renderer\" v-html=\"renderedContent\"></div>\n</template>\n\n<script>\nimport marked from 'marked'\nimport hljs from 'highlight.js'\n\nexport default {\n  name: 'MessageRenderer',\n  props: {\n    content: {\n      type: String,\n      required: true\n    }\n  },\n  computed: {\n    renderedContent() {\n      return this.renderMarkdown(this.content)\n    }\n  },\n  methods: {\n    renderMarkdown(content) {\n      // 配置marked选项\n      marked.setOptions({\n        highlight: function(code, language) {\n          if (language && hljs.getLanguage(language)) {\n            try {\n              return hljs.highlight(language, code).value\n            } catch (err) {\n              console.warn('代码高亮失败:', err)\n            }\n          }\n          return hljs.highlightAuto(code).value\n        },\n        breaks: true,\n        gfm: true,\n        tables: true,\n        sanitize: false\n      })\n      \n      return marked(content)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.message-renderer {\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 标题样式 */\n.message-renderer >>> h1 {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 16px 0 12px 0;\n  color: #2c3e50;\n  border-bottom: 2px solid #3498db;\n  padding-bottom: 8px;\n}\n\n.message-renderer >>> h2 {\n  font-size: 20px;\n  font-weight: bold;\n  margin: 14px 0 10px 0;\n  color: #34495e;\n  border-bottom: 1px solid #bdc3c7;\n  padding-bottom: 6px;\n}\n\n.message-renderer >>> h3 {\n  font-size: 18px;\n  font-weight: bold;\n  margin: 12px 0 8px 0;\n  color: #34495e;\n}\n\n.message-renderer >>> h4,\n.message-renderer >>> h5,\n.message-renderer >>> h6 {\n  font-size: 16px;\n  font-weight: bold;\n  margin: 10px 0 6px 0;\n  color: #34495e;\n}\n\n/* 段落样式 */\n.message-renderer >>> p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n/* 列表样式 */\n.message-renderer >>> ul,\n.message-renderer >>> ol {\n  margin: 8px 0;\n  padding-left: 24px;\n}\n\n.message-renderer >>> li {\n  margin: 4px 0;\n  line-height: 1.5;\n}\n\n/* 链接样式 */\n.message-renderer >>> a {\n  color: #3498db;\n  text-decoration: none;\n  border-bottom: 1px solid transparent;\n  transition: all 0.2s ease;\n}\n\n.message-renderer >>> a:hover {\n  color: #2980b9;\n  border-bottom-color: #2980b9;\n}\n\n/* 强调文本样式 */\n.message-renderer >>> strong {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.message-renderer >>> em {\n  font-style: italic;\n  color: #7f8c8d;\n}\n\n/* 行内代码样式 */\n.message-renderer >>> code {\n  background-color: #f8f9fa;\n  color: #e74c3c;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 0.9em;\n  border: 1px solid #e9ecef;\n}\n\n/* 代码块样式 */\n.message-renderer >>> pre {\n  background-color: #f8f9fa;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  padding: 16px;\n  margin: 12px 0;\n  overflow-x: auto;\n  position: relative;\n}\n\n.message-renderer >>> pre code {\n  background: none;\n  color: inherit;\n  padding: 0;\n  border: none;\n  border-radius: 0;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n/* 表格样式 */\n.message-renderer >>> table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 16px 0;\n  background-color: #fff;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.message-renderer >>> thead {\n  background-color: #f8f9fa;\n}\n\n.message-renderer >>> th {\n  padding: 12px 16px;\n  text-align: left;\n  font-weight: bold;\n  color: #2c3e50;\n  border-bottom: 2px solid #dee2e6;\n  font-size: 14px;\n}\n\n.message-renderer >>> td {\n  padding: 12px 16px;\n  border-bottom: 1px solid #dee2e6;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.message-renderer >>> tbody tr:hover {\n  background-color: #f8f9fa;\n}\n\n.message-renderer >>> tbody tr:last-child td {\n  border-bottom: none;\n}\n\n/* 引用块样式 */\n.message-renderer >>> blockquote {\n  border-left: 4px solid #3498db;\n  margin: 16px 0;\n  padding: 12px 16px;\n  background-color: #f8f9fa;\n  border-radius: 0 8px 8px 0;\n  color: #555;\n  font-style: italic;\n}\n\n.message-renderer >>> blockquote p {\n  margin: 0;\n}\n\n/* 分割线样式 */\n.message-renderer >>> hr {\n  border: none;\n  height: 2px;\n  background: linear-gradient(to right, transparent, #bdc3c7, transparent);\n  margin: 20px 0;\n}\n\n/* 图片样式 */\n.message-renderer >>> img {\n  max-width: 100%;\n  height: auto;\n  border-radius: 8px;\n  margin: 8px 0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n/* 复选框列表样式 */\n.message-renderer >>> input[type=\"checkbox\"] {\n  margin-right: 8px;\n  transform: scale(1.2);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .message-renderer >>> table {\n    font-size: 12px;\n  }\n  \n  .message-renderer >>> th,\n  .message-renderer >>> td {\n    padding: 8px 12px;\n  }\n  \n  .message-renderer >>> pre {\n    padding: 12px;\n    font-size: 12px;\n  }\n  \n  .message-renderer >>> h1 {\n    font-size: 20px;\n  }\n  \n  .message-renderer >>> h2 {\n    font-size: 18px;\n  }\n  \n  .message-renderer >>> h3 {\n    font-size: 16px;\n  }\n}\n</style>\n"]}]}