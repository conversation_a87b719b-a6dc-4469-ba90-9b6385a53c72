{"_from": "ejs@^2.6.1", "_id": "ejs@2.7.4", "_inBundle": false, "_integrity": "sha512-7vmuyh5+kuUyJKePhQfRQBhXV5Ce+RnaeeQArKu1EAMpL3WbgMt5WG6uQZpEVvYSSsxMXRKOewtDk9RaTKXRlA==", "_location": "/ejs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ejs@^2.6.1", "name": "ejs", "escapedName": "ejs", "rawSpec": "^2.6.1", "saveSpec": null, "fetchSpec": "^2.6.1"}, "_requiredBy": ["/webpack-bundle-analyzer"], "_resolved": "http://*************:4873/ejs/-/ejs-2.7.4.tgz", "_shasum": "48661287573dcc53e366c7a1ae52c3a120eec9ba", "_spec": "ejs@^2.6.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\webpack-bundle-analyzer", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://fleegix.org"}, "bugs": {"url": "https://github.com/mde/ejs/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Embedded JavaScript templates", "devDependencies": {"browserify": "^13.1.1", "eslint": "^4.14.0", "git-directory-deploy": "^1.5.1", "jake": "^10.3.1", "jsdoc": "^3.4.0", "lru-cache": "^4.0.1", "mocha": "^5.0.5", "uglify-js": "^3.3.16"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/mde/ejs", "keywords": ["template", "engine", "ejs"], "license": "Apache-2.0", "main": "./lib/ejs.js", "name": "ejs", "repository": {"type": "git", "url": "git://github.com/mde/ejs.git"}, "scripts": {"postinstall": "node ./postinstall.js", "test": "mocha"}, "version": "2.7.4"}