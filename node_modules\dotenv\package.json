{"_from": "dotenv@^8.2.0", "_id": "dotenv@8.6.0", "_inBundle": false, "_integrity": "sha512-IrPdXQsk2BbzvCBGBOTmmSH5SodmqZNt4ERAZDmW4CT+tL8VtvinqywuANaFu4bOMWki16nqf0e4oC0QIaDr/g==", "_location": "/dotenv", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dotenv@^8.2.0", "name": "dotenv", "escapedName": "dotenv", "rawSpec": "^8.2.0", "saveSpec": null, "fetchSpec": "^8.2.0"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://*************:4873/dotenv/-/dotenv-8.6.0.tgz", "_shasum": "061af664d19f7f4d8fc6e4ff9b584ce237adcb8b", "_spec": "dotenv@^8.2.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "bugs": {"url": "https://github.com/motdotla/dotenv/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Loads environment variables from .env file", "devDependencies": {"decache": "^4.5.1", "dtslint": "^0.9.8", "flow-bin": "^0.109.0", "sinon": "^7.5.0", "standard": "^13.1.0", "standard-markdown": "^5.1.0", "standard-version": "^7.0.0", "tap": "^14.7.0"}, "engines": {"node": ">=10"}, "exports": {".": "./lib/main.js", "./config": "./config.js", "./package.json": "./package.json"}, "homepage": "https://github.com/motdotla/dotenv#readme", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/main.js", "name": "dotenv", "repository": {"type": "git", "url": "git://github.com/motdotla/dotenv.git"}, "scripts": {"dtslint": "dtslint types", "flow": "flow", "lint": "standard", "postlint": "standard-markdown", "prerelease": "npm test", "pretest": "npm run lint && npm run dtslint", "release": "standard-version", "test": "tap tests/*.js --100"}, "standard": {"ignore": ["flow-typed/"]}, "types": "types/index.d.ts", "version": "8.6.0"}