{"_from": "css-loader@^3.5.3", "_id": "css-loader@3.6.0", "_inBundle": false, "_integrity": "sha512-M5lSukoWi1If8dhQAUCvj4H8vUt3vOnwbQBH9DdTm/s4Ym2B/3dPMtYZeJmq7Q3S3Pa+I94DcZ7pc9bP14cWIQ==", "_location": "/css-loader", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "css-loader@^3.5.3", "name": "css-loader", "escapedName": "css-loader", "rawSpec": "^3.5.3", "saveSpec": null, "fetchSpec": "^3.5.3"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://192.168.3.141:4873/css-loader/-/css-loader-3.6.0.tgz", "_shasum": "2e4b2c7e6e2d27f8c8f28f61bffcd2e6c91ef645", "_spec": "css-loader@^3.5.3", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack-contrib/css-loader/issues"}, "bundleDependencies": false, "dependencies": {"camelcase": "^5.3.1", "cssesc": "^3.0.0", "icss-utils": "^4.1.1", "loader-utils": "^1.2.3", "normalize-path": "^3.0.0", "postcss": "^7.0.32", "postcss-modules-extract-imports": "^2.0.0", "postcss-modules-local-by-default": "^3.0.2", "postcss-modules-scope": "^2.2.0", "postcss-modules-values": "^3.0.0", "postcss-value-parser": "^4.1.0", "schema-utils": "^2.7.0", "semver": "^6.3.0"}, "deprecated": false, "description": "css loader module for webpack", "devDependencies": {"@babel/cli": "^7.10.1", "@babel/core": "^7.10.2", "@babel/preset-env": "^7.10.2", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^25.5.1", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "es-check": "^5.1.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.21.2", "file-loader": "^6.0.0", "husky": "^4.2.5", "jest": "^25.5.4", "lint-staged": "^10.2.10", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "postcss-loader": "^3.0.0", "postcss-preset-env": "^6.7.0", "prettier": "^2.0.5", "sass": "^1.26.8", "sass-loader": "^8.0.2", "standard-version": "^8.0.0", "strip-ansi": "^6.0.0", "url-loader": "^4.1.0", "webpack": "^4.43.0"}, "engines": {"node": ">= 8.9.0"}, "files": ["dist"], "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "homepage": "https://github.com/webpack-contrib/css-loader", "keywords": ["webpack", "css", "loader", "url", "import"], "license": "MIT", "main": "dist/cjs.js", "name": "css-loader", "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/css-loader.git"}, "scripts": {"build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "defaults": "webpack-defaults", "lint": "npm-run-all -l -p \"lint:**\"", "lint:js": "eslint --cache .", "lint:prettier": "prettier --list-different .", "postbuild": "npm run validate:runtime", "prebuild": "npm run clean", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "security": "npm audit", "start": "npm run build -- -w", "test": "npm run test:coverage", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "validate:runtime": "es-check es5 \"dist/runtime/**/*.js\""}, "version": "3.6.0"}