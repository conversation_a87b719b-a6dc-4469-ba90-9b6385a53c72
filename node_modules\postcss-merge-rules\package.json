{"_from": "postcss-merge-rules@^4.0.3", "_id": "postcss-merge-rules@4.0.3", "_inBundle": false, "_integrity": "sha512-U7e3r1SbvYzO0Jr3UT/zKBVgYYyhAz0aitvGIYOYK5CPmkNih+WDSsS5tvPrJ8YMQYlEMvsZIiqmn7HdFUaeEQ==", "_location": "/postcss-merge-rules", "_phantomChildren": {"dot-prop": "5.3.0", "indexes-of": "1.0.1", "source-map": "0.6.1", "uniq": "1.0.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-merge-rules@^4.0.3", "name": "postcss-merge-rules", "escapedName": "postcss-merge-rules", "rawSpec": "^4.0.3", "saveSpec": null, "fetchSpec": "^4.0.3"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "http://192.168.3.141:4873/postcss-merge-rules/-/postcss-merge-rules-4.0.3.tgz", "_shasum": "362bea4ff5a1f98e4075a713c6cb25aefef9a650", "_spec": "postcss-merge-rules@^4.0.3", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cssnano-preset-default", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"browserslist": "^4.0.0", "caniuse-api": "^3.0.0", "cssnano-util-same-parent": "^4.0.0", "postcss": "^7.0.0", "postcss-selector-parser": "^3.0.0", "vendors": "^1.0.0"}, "deprecated": false, "description": "Merge CSS rules with PostCSS.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "postcss-discard-comments": "^4.0.0", "postcss-simple-vars": "^5.0.1"}, "engines": {"node": ">=6.9.0"}, "files": ["LICENSE-MIT", "dist"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["css", "optimise", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-merge-rules", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.3"}