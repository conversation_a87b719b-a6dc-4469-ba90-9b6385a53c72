{"_from": "postcss-colormin@^4.0.3", "_id": "postcss-colormin@4.0.3", "_inBundle": false, "_integrity": "sha512-WyQFAdDZpExQh32j0U0feWisZ0dmOtPl44qYmJKkq9xFWY3p+4qnRzCHeNrkeRhwPHz9bQ3mo0/yVkaply0MNw==", "_location": "/postcss-colormin", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-colormin@^4.0.3", "name": "postcss-colormin", "escapedName": "postcss-colormin", "rawSpec": "^4.0.3", "saveSpec": null, "fetchSpec": "^4.0.3"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "http://192.168.3.141:4873/postcss-colormin/-/postcss-colormin-4.0.3.tgz", "_shasum": "ae060bce93ed794ac71264f08132d550956bd381", "_spec": "postcss-colormin@^4.0.3", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cssnano-preset-default", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"browserslist": "^4.0.0", "color": "^3.0.0", "has": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "deprecated": false, "description": "Minify colors in your CSS files with PostCSS.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "css-color-names": "0.0.4", "write-file": "^1.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["dist", "LICENSE-MIT"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["color", "colors", "compression", "css", "minify", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-colormin", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel-node ./src/generate.js && babel src --out-dir dist --ignore /__tests__/,src/generate.js"}, "version": "4.0.3"}