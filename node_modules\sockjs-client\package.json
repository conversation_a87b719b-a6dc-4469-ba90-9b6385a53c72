{"_from": "sockjs-client@^1.5.0", "_id": "sockjs-client@1.6.1", "_inBundle": false, "_integrity": "sha512-2g0tjOR+fRs0amxENLi/q5TiJTqY+WXFOzb5UwXndlK6TO3U/mirZznpx6w34HVMoc3g7cY24yC/ZMIYnDlfkw==", "_location": "/sockjs-client", "_phantomChildren": {"ms": "2.1.3"}, "_requested": {"type": "range", "registry": true, "raw": "sockjs-client@^1.5.0", "name": "sockjs-client", "escapedName": "sockjs-client", "rawSpec": "^1.5.0", "saveSpec": null, "fetchSpec": "^1.5.0"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "http://192.168.3.141:4873/sockjs-client/-/sockjs-client-1.6.1.tgz", "_shasum": "350b8eda42d6d52ddc030c39943364c11dcad806", "_spec": "sockjs-client@^1.5.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\webpack-dev-server", "author": {"name": "<PERSON>"}, "browser": {"./lib/transport/driver/websocket.js": "./lib/transport/browser/websocket.js", "eventsource": "./lib/transport/browser/eventsource.js", "./lib/transport/driver/xhr.js": "./lib/transport/browser/abstract-xhr.js", "crypto": "./lib/utils/browser-crypto.js", "events": "./lib/event/emitter.js"}, "bugs": {"url": "https://github.com/sockjs/sockjs-client/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"debug": "^3.2.7", "eventsource": "^2.0.2", "faye-websocket": "^0.11.4", "inherits": "^2.0.4", "url-parse": "^1.5.10"}, "deprecated": false, "description": "SockJS-client is a browser JavaScript library that provides a WebSocket-like object.", "devDependencies": {"browserify": "^17.0.0", "envify": "^4.0.0", "eslint": "^8.10.0", "expect.js": "~0.3.1", "gulp": "^4.0.2", "gulp-header": "^2.0.9", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.3", "gulp-sourcemaps": "^3.0.0", "gulp-uglify": "^3.0.2", "karma": "^6.3.16", "karma-browserify": "^8.1.0", "karma-browserstack-launcher": "^1.6.0", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "mocha": "^9.2.1", "proxyquire": "^2.1.3", "pump": "^3.0.0", "serve-static": "^1.14.2", "sockjs": "^0.3.24", "vinyl-buffer": "~1.0.0", "vinyl-source-stream": "^2.0.0"}, "engines": {"node": ">=12"}, "funding": "https://tidelift.com/funding/github/npm/sockjs-client", "homepage": "http://sockjs.org", "jsdelivr": "dist/sockjs.min.js", "keywords": ["websockets", "websocket"], "license": "MIT", "main": "./lib/entry.js", "name": "sockjs-client", "repository": {"type": "git", "url": "git+https://github.com/sockjs/sockjs-client.git"}, "scripts": {"gulp": "gulp", "lint": "eslint .", "postpublish": "git push origin --all && git push origin --tags", "postversion": "npm publish", "test": "mocha tests/node.js", "test:browser_local": "npm run test:bundle && npx karma start --browsers Chrome", "test:browser_remote": "npm run test:bundle && npx karma start", "test:bundle": "gulp testbundle", "version": "gulp release && git add -A dist lib/version.js Changelog.md"}, "version": "1.6.1"}