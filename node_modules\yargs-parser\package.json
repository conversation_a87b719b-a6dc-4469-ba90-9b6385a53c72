{"_from": "yargs-parser@^20.2.2", "_id": "yargs-parser@20.2.9", "_inBundle": false, "_integrity": "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==", "_location": "/yargs-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "yargs-parser@^20.2.2", "name": "yargs-parser", "escapedName": "yargs-parser", "rawSpec": "^20.2.2", "saveSpec": null, "fetchSpec": "^20.2.2"}, "_requiredBy": ["/yargs"], "_resolved": "http://*************:4873/yargs-parser/-/yargs-parser-20.2.9.tgz", "_shasum": "2eb7dc3b0289718fc295f362753845c41a0c94ee", "_spec": "yargs-parser@^20.2.2", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "bundleDependencies": false, "deprecated": false, "description": "the mighty option parser used by yargs", "devDependencies": {"@types/chai": "^4.2.11", "@types/mocha": "^8.0.0", "@types/node": "^14.0.0", "@typescript-eslint/eslint-plugin": "^3.10.1", "@typescript-eslint/parser": "^3.10.1", "@wessberg/rollup-plugin-ts": "^1.2.28", "c8": "^7.3.0", "chai": "^4.2.0", "cross-env": "^7.0.2", "eslint": "^7.0.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "gts": "^3.0.0", "mocha": "^9.0.0", "puppeteer": "^10.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "rollup-plugin-cleanup": "^3.1.1", "serve": "^12.0.0", "standardx": "^7.0.0", "start-server-and-test": "^1.11.2", "ts-transform-default-export": "^1.0.2", "typescript": "^4.0.0"}, "engines": {"node": ">=10"}, "exports": {".": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "files": ["browser.js", "build", "!*.d.ts"], "homepage": "https://github.com/yargs/yargs-parser#readme", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "license": "ISC", "main": "build/index.cjs", "module": "./build/lib/index.js", "name": "yargs-parser", "repository": {"type": "git", "url": "git+https://github.com/yargs/yargs-parser.git"}, "scripts": {"build:cjs": "rollup -c", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "coverage": "c8 report --check-coverage", "fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "postcompile": "npm run build:cjs", "precompile": "<PERSON><PERSON><PERSON> build", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "pretest:typescript": "npm run pretest", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js"}, "standardx": {"ignore": ["build"]}, "type": "module", "version": "20.2.9"}