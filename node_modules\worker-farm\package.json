{"_from": "worker-farm@^1.7.0", "_id": "worker-farm@1.7.0", "_inBundle": false, "_integrity": "sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw==", "_location": "/worker-farm", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "worker-farm@^1.7.0", "name": "worker-farm", "escapedName": "worker-farm", "rawSpec": "^1.7.0", "saveSpec": null, "fetchSpec": "^1.7.0"}, "_requiredBy": ["/terser-webpack-plugin"], "_resolved": "http://*************:4873/worker-farm/-/worker-farm-1.7.0.tgz", "_shasum": "26a94c5391bbca926152002f69b84a4bf772e5a8", "_spec": "worker-farm@^1.7.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\terser-webpack-plugin", "authors": ["<PERSON> Vagg @rvagg <<EMAIL>> (https://github.com/rvagg)"], "bugs": {"url": "https://github.com/rvagg/node-worker-farm/issues"}, "bundleDependencies": false, "dependencies": {"errno": "~0.1.7"}, "deprecated": false, "description": "Distribute processing tasks to child processes with an über-simple API and baked-in durability & custom concurrency options.", "devDependencies": {"tape": "~4.10.1"}, "homepage": "https://github.com/rvagg/node-worker-farm", "keywords": ["worker", "child", "processing", "farm"], "license": "MIT", "main": "./lib/index.js", "name": "worker-farm", "repository": {"type": "git", "url": "git+https://github.com/rvagg/node-worker-farm.git"}, "scripts": {"test": "node ./tests/"}, "types": "./index.d.ts", "version": "1.7.0"}