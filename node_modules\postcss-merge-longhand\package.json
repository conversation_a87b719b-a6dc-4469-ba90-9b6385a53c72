{"_from": "postcss-merge-longhand@^4.0.11", "_id": "postcss-merge-longhand@4.0.11", "_inBundle": false, "_integrity": "sha512-alx/zmoeXvJjp7L4mxEMjh8lxVlDFX1gqWHzaaQewwMZiVhLo42TEClKaeHbRf6J7j82ZOdTJ808RtN0ZOZwvw==", "_location": "/postcss-merge-longhand", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-merge-longhand@^4.0.11", "name": "postcss-merge-longhand", "escapedName": "postcss-merge-longhand", "rawSpec": "^4.0.11", "saveSpec": null, "fetchSpec": "^4.0.11"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "http://192.168.3.141:4873/postcss-merge-longhand/-/postcss-merge-longhand-4.0.11.tgz", "_shasum": "62f49a13e4a0ee04e7b98f42bb16062ca2549e24", "_spec": "postcss-merge-longhand@^4.0.11", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cssnano-preset-default", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"css-color-names": "0.0.4", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0", "stylehacks": "^4.0.0"}, "deprecated": false, "description": "Merge longhand properties into shorthand with PostCSS.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["LICENSE-MIT", "dist"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["css", "minify", "optimise", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-merge-longhand", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.11"}