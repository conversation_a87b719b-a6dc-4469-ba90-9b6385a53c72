{"_from": "electron-to-chromium@^1.5.173", "_id": "electron-to-chromium@1.5.194", "_inBundle": false, "_integrity": "sha512-SdnWJwSUot04UR51I2oPD8kuP2VI37/CADR1OHsFOUzZIvfWJBO6q11k5P/uKNyTT3cdOsnyjkrZ+DDShqYqJA==", "_location": "/electron-to-chromium", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "electron-to-chromium@^1.5.173", "name": "electron-to-chromium", "escapedName": "electron-to-chromium", "rawSpec": "^1.5.173", "saveSpec": null, "fetchSpec": "^1.5.173"}, "_requiredBy": ["/browserslist"], "_resolved": "http://*************:4873/electron-to-chromium/-/electron-to-chromium-1.5.194.tgz", "_shasum": "05e541c3373ba8d967a65c92bc14d60608908236", "_spec": "electron-to-chromium@^1.5.173", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\browserslist", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/kilian/electron-to-chromium/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Provides a list of electron-to-chromium version mappings", "devDependencies": {"ava": "^5.1.1", "codecov": "^3.8.2", "compare-versions": "^6.0.0-rc.1", "node-fetch": "^3.3.0", "nyc": "^15.1.0", "shelljs": "^0.8.5"}, "files": ["versions.js", "full-versions.js", "chromium-versions.js", "full-chromium-versions.js", "versions.json", "full-versions.json", "chromium-versions.json", "full-chromium-versions.json", "LICENSE"], "homepage": "https://github.com/kilian/electron-to-chromium#readme", "keywords": ["electron", "chrome", "chromium", "browserslist", "browserlist"], "license": "ISC", "main": "index.js", "name": "electron-to-chromium", "repository": {"type": "git", "url": "git+https://github.com/kilian/electron-to-chromium.git"}, "scripts": {"build": "node build.mjs", "report": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "test": "nyc ava --verbose", "update": "node automated-update.js"}, "version": "1.5.194"}