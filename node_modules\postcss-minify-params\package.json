{"_from": "postcss-minify-params@^4.0.2", "_id": "postcss-minify-params@4.0.2", "_inBundle": false, "_integrity": "sha512-G7eWyzEx0xL4/wiBBJxJOz48zAKV2WG3iZOqVhPet/9geefm/Px5uo1fzlHu+DOjT+m0Mmiz3jkQzVHe6wxAWg==", "_location": "/postcss-minify-params", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-minify-params@^4.0.2", "name": "postcss-minify-params", "escapedName": "postcss-minify-params", "rawSpec": "^4.0.2", "saveSpec": null, "fetchSpec": "^4.0.2"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "http://192.168.3.141:4873/postcss-minify-params/-/postcss-minify-params-4.0.2.tgz", "_shasum": "6b9cef030c11e35261f95f618c90036d680db874", "_spec": "postcss-minify-params@^4.0.2", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cssnano-preset-default", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"alphanum-sort": "^1.0.0", "browserslist": "^4.0.0", "cssnano-util-get-arguments": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0", "uniqs": "^2.0.0"}, "deprecated": false, "description": "Minify at-rule params with PostCSS", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["dist"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["postcss", "css", "postcss-plugin", "minify", "optimise", "params"], "license": "MIT", "main": "dist/index.js", "name": "postcss-minify-params", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.2"}