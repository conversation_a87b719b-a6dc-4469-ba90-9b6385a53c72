{"_from": "easy-stack@1.0.1", "_id": "easy-stack@1.0.1", "_inBundle": false, "_integrity": "sha512-wK2sCs4feiiJeFXn3zvY0p41mdU5VUgbgs1rNsc/y5ngFUijdWd+iIN8eoyuZHKB8xN6BL4PdWmzqFmxNg6V2w==", "_location": "/easy-stack", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "easy-stack@1.0.1", "name": "easy-stack", "escapedName": "easy-stack", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/@node-ipc/js-queue"], "_resolved": "http://*************:4873/easy-stack/-/easy-stack-1.0.1.tgz", "_shasum": "8afe4264626988cabb11f3c704ccd0c835411066", "_spec": "easy-stack@1.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@node-ipc\\js-queue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/RIAEvangelist/easy-stack/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Simple JS stack with auto run for node and browsers", "engines": {"node": ">=6.0.0"}, "homepage": "https://github.com/RIAEvangelist/easy-stack#readme", "keywords": ["stack", "node", "js", "auto", "run", "execute", "browser", "react"], "license": "MIT", "main": "stack.js", "name": "easy-stack", "repository": {"type": "git", "url": "git+https://github.com/RIAEvangelist/easy-stack.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.1"}