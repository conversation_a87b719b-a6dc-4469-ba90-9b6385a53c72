{"_from": "@vue/component-compiler-utils@^3.1.2", "_id": "@vue/component-compiler-utils@3.3.0", "_inBundle": false, "_integrity": "sha512-97sfH2mYNU+2PzGrmK2haqffDpVASuib9/w2/noxiFi31Z54hW+q3izKQXXQZSNhtiUpAI36uSuYepeBe4wpHQ==", "_location": "/@vue/component-compiler-utils", "_phantomChildren": {"pseudomap": "1.0.2", "source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "@vue/component-compiler-utils@^3.1.2", "name": "@vue/component-compiler-utils", "escapedName": "@vue%2fcomponent-compiler-utils", "scope": "@vue", "rawSpec": "^3.1.2", "saveSpec": null, "fetchSpec": "^3.1.2"}, "_requiredBy": ["/@vue/cli-service", "/vue-loader"], "_resolved": "http://192.168.3.141:4873/@vue/component-compiler-utils/-/component-compiler-utils-3.3.0.tgz", "_shasum": "f9f5fb53464b0c37b2c8d2f3fbfe44df60f61dc9", "_spec": "@vue/component-compiler-utils@^3.1.2", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/component-compiler-utils/issues"}, "bundleDependencies": false, "dependencies": {"consolidate": "^0.15.1", "hash-sum": "^1.0.2", "lru-cache": "^4.1.2", "merge-source-map": "^1.1.0", "postcss": "^7.0.36", "postcss-selector-parser": "^6.0.2", "prettier": "^1.18.2 || ^2.0.0", "source-map": "~0.6.1", "vue-template-es2015-compiler": "^1.9.0"}, "deprecated": false, "description": "Lower level utilities for compiling Vue single file components", "devDependencies": {"@types/jest": "^22.2.3", "@types/node": "^10.12.20", "conventional-changelog-cli": "^2.0.11", "jest": "^24.0.0", "less": "^3.9.0", "lint-staged": "^8.1.1", "pug": "^2.0.3", "sass": "^1.17.3", "stylus": "^0.54.5", "ts-jest": "^24.0.0", "typescript": "^3.3.0", "vue": "^2.6.6", "vue-template-compiler": "^2.6.6", "yorkie": "^2.0.0"}, "files": ["dist", "lib"], "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://github.com/vuejs/component-compiler-utils#readme", "keywords": ["vue", "sfc", "component", "compiler"], "license": "MIT", "lint-staged": {"*.{ts,js}": ["prettier --write", "git add"]}, "main": "dist/index.js", "name": "@vue/component-compiler-utils", "optionalDependencies": {"prettier": "^1.18.2 || ^2.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/component-compiler-utils.git"}, "scripts": {"build": "rm -rf dist && tsc", "lint": "prettier --write \"{lib,test}/**/*.ts\"", "prepublishOnly": "yarn build && conventional-changelog -p angular -r 2 -i CHANGELOG.md -s", "test": "prettier --list-different \"{lib,test}/**/*.ts\" && jest --coverage"}, "typings": "dist/index.d.ts", "version": "3.3.0"}