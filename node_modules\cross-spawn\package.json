{"_from": "cross-spawn@^6.0.0", "_id": "cross-spawn@6.0.6", "_inBundle": false, "_integrity": "sha512-VqCUuhcd1iB+dsv8gxPttb5iZh/D0iubSP21g36KXdEuf6I5JiioesUVjpCdHV9MZRUfVFlvwtIUyPfxo5trtw==", "_location": "/cross-spawn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cross-spawn@^6.0.0", "name": "cross-spawn", "escapedName": "cross-spawn", "rawSpec": "^6.0.0", "saveSpec": null, "fetchSpec": "^6.0.0"}, "_requiredBy": ["/execa"], "_resolved": "http://192.168.3.141:4873/cross-spawn/-/cross-spawn-6.0.6.tgz", "_shasum": "30d0efa0712ddb7eb5a76e1e8721bffafa6b5d57", "_spec": "cross-spawn@^6.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\execa", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "bundleDependencies": false, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "deprecated": false, "description": "Cross platform child_process#spawn and child_process#spawnSync", "devDependencies": {"@commitlint/cli": "^6.0.0", "@commitlint/config-conventional": "^6.0.2", "babel-core": "^6.26.0", "babel-jest": "^22.1.0", "babel-preset-moxy": "^2.2.1", "eslint": "^4.3.0", "eslint-config-moxy": "^5.0.0", "husky": "^0.14.3", "jest": "^22.0.0", "lint-staged": "^7.0.0", "mkdirp": "^0.5.1", "regenerator-runtime": "^0.11.1", "rimraf": "^2.6.2", "standard-version": "^4.2.0"}, "engines": {"node": ">=4.8"}, "files": ["lib"], "homepage": "https://github.com/moxystudio/node-cross-spawn", "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "license": "MIT", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "main": "index.js", "name": "cross-spawn", "repository": {"type": "git", "url": "git+ssh://**************/moxystudio/node-cross-spawn.git"}, "scripts": {"commitmsg": "commitlint -e $GIT_PARAMS", "lint": "eslint .", "precommit": "lint-staged", "release": "standard-version", "test": "jest --env node --coverage"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin v6"}}, "version": "6.0.6"}