{"_from": "webpack-dev-server@^3.11.0", "_id": "webpack-dev-server@3.11.3", "_inBundle": false, "_integrity": "sha512-3x31rjbEQWKMNzacUZRE6wXvUFuGpH7vr0lIEbYpMAG9BOxi0928QU1BBswOAP3kg3H1O4hiS+sq4YyAn6ANnA==", "_location": "/webpack-dev-server", "_phantomChildren": {"ajv": "6.12.6", "ajv-errors": "1.0.1", "ajv-keywords": "3.5.2", "ansi-styles": "3.2.1", "arr-diff": "4.0.0", "arr-flatten": "1.1.0", "array-unique": "0.3.2", "async-each": "1.0.6", "camelcase": "5.3.1", "decamelize": "1.2.0", "define-property": "2.0.2", "extend-shallow": "3.0.2", "extglob": "2.0.4", "fragment-cache": "0.2.1", "get-caller-file": "2.0.5", "glob-parent": "3.1.0", "graceful-fs": "4.2.11", "has-flag": "3.0.0", "http-proxy": "1.18.1", "inherits": "2.0.4", "is-buffer": "1.1.6", "is-extendable": "0.1.1", "is-glob": "4.0.3", "isobject": "3.0.1", "kind-of": "6.0.3", "lodash": "4.17.21", "nanomatch": "1.2.13", "normalize-path": "3.0.0", "object.pick": "1.3.0", "p-limit": "2.3.0", "path-is-absolute": "1.0.1", "readable-stream": "2.3.8", "regex-not": "1.0.2", "remove-trailing-separator": "1.1.0", "repeat-element": "1.1.4", "repeat-string": "1.6.1", "require-directory": "2.1.1", "require-main-filename": "2.0.0", "set-blocking": "2.0.0", "snapdragon": "0.8.2", "snapdragon-node": "2.1.1", "split-string": "3.1.0", "to-regex": "3.0.2", "upath": "1.2.0", "which-module": "2.0.1"}, "_requested": {"type": "range", "registry": true, "raw": "webpack-dev-server@^3.11.0", "name": "webpack-dev-server", "escapedName": "webpack-dev-server", "rawSpec": "^3.11.0", "saveSpec": null, "fetchSpec": "^3.11.0"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://192.168.3.141:4873/webpack-dev-server/-/webpack-dev-server-3.11.3.tgz", "_shasum": "8c86b9d2812bf135d3c9bce6f07b718e30f7c3d3", "_spec": "webpack-dev-server@^3.11.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON> @sokra"}, "bin": {"webpack-dev-server": "bin/webpack-dev-server.js"}, "bugs": {"url": "https://github.com/webpack/webpack-dev-server/issues"}, "bundleDependencies": false, "dependencies": {"ansi-html-community": "0.0.8", "bonjour": "^3.5.0", "chokidar": "^2.1.8", "compression": "^1.7.4", "connect-history-api-fallback": "^1.6.0", "debug": "^4.1.1", "del": "^4.1.1", "express": "^4.17.1", "html-entities": "^1.3.1", "http-proxy-middleware": "0.19.1", "import-local": "^2.0.0", "internal-ip": "^4.3.0", "ip": "^1.1.5", "is-absolute-url": "^3.0.3", "killable": "^1.0.1", "loglevel": "^1.6.8", "opn": "^5.5.0", "p-retry": "^3.0.1", "portfinder": "^1.0.26", "schema-utils": "^1.0.0", "selfsigned": "^1.10.8", "semver": "^6.3.0", "serve-index": "^1.9.1", "sockjs": "^0.3.21", "sockjs-client": "^1.5.0", "spdy": "^4.0.2", "strip-ansi": "^3.0.1", "supports-color": "^6.1.0", "url": "^0.11.0", "webpack-dev-middleware": "^3.7.2", "webpack-log": "^2.0.0", "ws": "^6.2.1", "yargs": "^13.3.2"}, "deprecated": false, "description": "Serves a webpack app. Updates the browser on changes.", "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/plugin-transform-runtime": "^7.9.6", "@babel/preset-env": "^7.9.6", "@babel/runtime": "^7.9.6", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "babel-loader": "^8.1.0", "body-parser": "^1.19.0", "commitlint-azure-pipelines-cli": "^1.0.3", "copy-webpack-plugin": "^5.1.1", "css-loader": "^2.1.1", "eslint": "^6.8.0", "eslint-config-prettier": "^6.11.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-import": "^2.20.2", "execa": "^1.0.0", "file-loader": "^5.1.0", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.2.0", "husky": "^4.2.5", "jest": "^24.9.0", "jest-junit": "^10.0.0", "jquery": "^3.5.1", "less": "^3.11.1", "less-loader": "^5.0.0", "lint-staged": "^10.2.2", "marked": "^0.8.2", "memfs": "^3.1.2", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "puppeteer": "^1.20.0", "rimraf": "^3.0.2", "standard-version": "^8.0.0", "style-loader": "^1.2.1", "supertest": "^4.0.2", "tcp-port-used": "^1.0.1", "typescript": "^3.8.3", "url-loader": "^3.0.0", "webpack": "^4.43.0", "webpack-cli": "^3.3.11"}, "engines": {"node": ">= 6.11.5"}, "files": ["bin", "lib", "ssl", "client"], "homepage": "https://github.com/webpack/webpack-dev-server#readme", "license": "MIT", "main": "lib/Server.js", "name": "webpack-dev-server", "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/webpack/webpack-dev-server.git"}, "scripts": {"build:client": "rimraf ./client/* && npm-run-all -s -l -p \"build:client:**\"", "build:client:clients": "babel client-src/clients --out-dir client/clients", "build:client:default": "babel client-src/default --out-dir client --ignore \"./client-src/default/*.config.js\"", "build:client:index": "webpack ./client-src/default/index.js -o client/index.bundle.js --color --config client-src/default/webpack.config.js", "build:client:live": "webpack ./client-src/live/index.js -o client/live.bundle.js --color --config client-src/live/webpack.config.js", "build:client:sockjs": "webpack ./client-src/sockjs/index.js -o client/sockjs.bundle.js --color --config client-src/sockjs/webpack.config.js", "commitlint": "commitlint --from=master", "lint": "npm-run-all -l -p \"lint:**\"", "lint:js": "eslint . --cache", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:type": "tsc --noEmit", "prepare": "rimraf ./ssl/*.pem && npm run build:client", "pretest": "npm run lint", "release": "standard-version", "security": "npm audit", "test": "npm run test:coverage", "test:coverage": "npm run test:only -- --coverage", "test:only": "jest --force<PERSON>xit", "test:watch": "npm run test:coverage --watch", "webpack-dev-server": "node examples/run-example.js"}, "version": "3.11.3"}