{"_from": "webpack@^4.0.0", "_id": "webpack@4.47.0", "_inBundle": false, "_integrity": "sha512-td7fYwgLSrky3fI1EuU5cneU4+pbH6GgOfuKNS1tNPcfdGinGELAqsb/BP4nnvZyKSG2i/xFGU7+n2PvZA8HJQ==", "_location": "/webpack", "_phantomChildren": {"ajv": "6.12.6", "ajv-errors": "1.0.1", "ajv-keywords": "3.5.2", "arr-diff": "4.0.0", "arr-flatten": "1.1.0", "array-unique": "0.3.2", "define-property": "2.0.2", "extend-shallow": "3.0.2", "extglob": "2.0.4", "fragment-cache": "0.2.1", "is-buffer": "1.1.6", "is-extendable": "0.1.1", "isobject": "3.0.1", "kind-of": "6.0.3", "nanomatch": "1.2.13", "object.pick": "1.3.0", "regex-not": "1.0.2", "repeat-element": "1.1.4", "repeat-string": "1.6.1", "snapdragon": "0.8.2", "snapdragon-node": "2.1.1", "split-string": "3.1.0", "to-regex": "3.0.2"}, "_requested": {"type": "range", "registry": true, "raw": "webpack@^4.0.0", "name": "webpack", "escapedName": "webpack", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://192.168.3.141:4873/webpack/-/webpack-4.47.0.tgz", "_shasum": "8b8a02152d7076aeb03b61b47dad2eeed9810ebc", "_spec": "webpack@^4.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON> @sokra"}, "bin": {"webpack": "bin/webpack.js"}, "bugs": {"url": "https://github.com/webpack/webpack/issues"}, "bundleDependencies": false, "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/wasm-edit": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "acorn": "^6.4.1", "ajv": "^6.10.2", "ajv-keywords": "^3.4.1", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^4.5.0", "eslint-scope": "^4.0.3", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.4.0", "loader-utils": "^1.2.3", "memory-fs": "^0.4.1", "micromatch": "^3.1.10", "mkdirp": "^0.5.3", "neo-async": "^2.6.1", "node-libs-browser": "^2.2.1", "schema-utils": "^1.0.0", "tapable": "^1.1.3", "terser-webpack-plugin": "^1.4.3", "watchpack": "^1.7.4", "webpack-sources": "^1.4.1"}, "deprecated": false, "description": "Packs CommonJs/AMD modules for the browser. Allows to split your codebase into multiple bundles, which can be loaded on demand. Support loaders to preprocess files, i.e. json, jsx, es7, css, less, ... and your custom stuff.", "devDependencies": {"@babel/core": "^7.7.2", "@types/node": "^10.12.21", "@types/tapable": "^1.0.1", "@types/webpack-sources": "^0.1.4", "@yarnpkg/lockfile": "^1.1.0", "babel-loader": "^8.0.6", "benchmark": "^2.1.1", "bundle-loader": "~0.5.0", "coffee-loader": "^0.9.0", "coffeescript": "^2.3.2", "coveralls": "^3.0.2", "css-loader": "^2.1.0", "es6-promise-polyfill": "^1.1.1", "eslint": "^5.8.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-jest": "^22.2.2", "eslint-plugin-jsdoc": "^15.3.2", "eslint-plugin-node": "^8.0.0", "eslint-plugin-prettier": "^3.0.0", "express": "~4.16.4", "file-loader": "^3.0.1", "glob": "^7.1.3", "husky": "^1.1.3", "i18n-webpack-plugin": "^1.0.0", "istanbul": "^0.4.5", "jest": "^24.9.0", "jest-junit": "^8.0.0", "json-loader": "^0.5.7", "json-schema-to-typescript": "^6.0.1", "less": "^3.9.0", "less-loader": "^4.0.3", "lint-staged": "^8.0.4", "lodash": "^4.17.4", "prettier": "^1.14.3", "pug": "^2.0.4", "pug-loader": "^2.4.0", "raw-loader": "^1.0.0", "react": "^16.8.0", "react-dom": "^16.8.0", "rimraf": "^2.6.2", "script-loader": "~0.7.0", "simple-git": "^1.65.0", "strip-ansi": "^5.2.0", "style-loader": "^0.23.1", "typescript": "^3.0.0-rc", "url-loader": "^1.1.2", "val-loader": "^1.0.2", "vm-browserify": "~1.1.0", "wast-loader": "^1.5.5", "webassembly-feature": "1.3.0", "webpack-dev-middleware": "^3.5.1", "worker-loader": "^2.0.0", "xxhashjs": "^0.2.1"}, "engines": {"node": ">=6.11.5"}, "files": ["lib/", "bin/", "buildin/", "declarations/", "hot/", "web_modules/", "schemas/", "SECURITY.md"], "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "homepage": "https://github.com/webpack/webpack", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "jest": {"forceExit": true, "setupFilesAfterEnv": ["<rootDir>/test/setupTestFramework.js"], "testMatch": ["<rootDir>/test/*.test.js", "<rootDir>/test/*.unittest.js"], "watchPathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "modulePathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules/webpack/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "transformIgnorePatterns": ["<rootDir>"], "coverageDirectory": "<rootDir>/coverage", "coveragePathIgnorePatterns": ["\\.runtime\\.js$", "<rootDir>/test", "<rootDir>/schemas", "<rootDir>/node_modules"], "testEnvironment": "node", "coverageReporters": ["json"]}, "license": "MIT", "lint-staged": {"*.js|{lib,setup,bin,hot,buildin,tooling,schemas}/**/*.js|test/*.js|{test,examples}/**/webpack.config.js}": ["eslint --cache"]}, "main": "lib/webpack.js", "name": "webpack", "peerDependenciesMeta": {"webpack-cli": {"optional": true}, "webpack-command": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/webpack/webpack.git"}, "scripts": {"appveyor:benchmark": "yarn benchmark --ci", "appveyor:integration": "yarn cover:integration --ci %JEST%", "appveyor:unit": "yarn cover:unit --ci %JEST%", "benchmark": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.benchmark.js\" --runInBand", "build:examples": "cd examples && node buildAll.js", "code-lint": "eslint . --ext '.js' --cache", "cover": "yarn cover:all && yarn cover:report", "cover:all": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --coverage", "cover:basic": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/te{st/TestCasesNormal,st/StatsTestCases,st/ConfigTestCases}.test.js\" --coverage", "cover:integration": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.test.js\" --coverage", "cover:report": "istanbul report", "cover:unit": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\" --coverage", "fix": "yarn code-lint --fix && yarn special-lint-fix", "jest-lint": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.lint.js\" --no-verbose", "lint": "yarn code-lint && yarn jest-lint && yarn type-lint && yarn special-lint", "prelint": "yarn setup", "pretest": "yarn lint", "pretty": "prettier --loglevel warn --write \"*.{ts,js,json,yml,yaml}\" \"{setup,lib,bin,hot,buildin,benchmark,tooling,schemas}/**/*.{js,json}\" \"test/*.js\" \"test/helpers/*.js\" \"test/{configCases,watchCases,statsCases,hotCases}/**/webpack.config.js\" \"examples/**/webpack.config.js\"", "setup": "node ./setup/setup.js", "special-lint": "node tooling/inherit-types && node tooling/format-schemas && node tooling/compile-to-definitions", "special-lint-fix": "node tooling/inherit-types --write --override && node tooling/format-schemas --write && node tooling/compile-to-definitions --write", "test": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest", "test:basic": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/te{st/TestCasesNormal,st/StatsTestCases,st/ConfigTestCases}.test.js\"", "test:integration": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.test.js\"", "test:unit": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\"", "test:update-snapshots": "yarn jest -u", "travis:basic": "yarn cover:basic --ci $JEST", "travis:benchmark": "yarn benchmark --ci", "travis:integration": "yarn cover:integration --ci $JEST", "travis:lintunit": "yarn lint && yarn cover:unit --ci $JEST", "type-lint": "tsc --pretty"}, "version": "4.47.0", "web": "lib/webpack.web.js"}