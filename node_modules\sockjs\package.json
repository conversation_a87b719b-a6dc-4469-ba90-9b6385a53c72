{"_from": "sockjs@^0.3.21", "_id": "sockjs@0.3.24", "_inBundle": false, "_integrity": "sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==", "_location": "/sockjs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "sockjs@^0.3.21", "name": "sockjs", "escapedName": "sockjs", "rawSpec": "^0.3.21", "saveSpec": null, "fetchSpec": "^0.3.21"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "http://192.168.3.141:4873/sockjs/-/sockjs-0.3.24.tgz", "_shasum": "c9bc8995f33a111bea0395ec30aa3206bdb5ccce", "_spec": "sockjs@^0.3.21", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\webpack-dev-server", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/sockjs/sockjs-node/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"faye-websocket": "^0.11.3", "uuid": "^8.3.2", "websocket-driver": "^0.7.4"}, "deprecated": false, "description": "SockJS-node is a server counterpart of SockJS-client a JavaScript library that provides a WebSocket-like object in the browser. SockJS gives you a coherent, cross-browser, Javascript API which creates a low latency, full duplex, cross-domain communication channel between the browser and the web server.", "devDependencies": {"coffeescript": "^1.12.7"}, "homepage": "https://github.com/sockjs/sockjs-node", "keywords": ["websockets", "websocket"], "license": "MIT", "main": "index", "name": "sockjs", "repository": {"type": "git", "url": "git+https://github.com/sockjs/sockjs-node.git"}, "scripts": {"postpublish": "git push origin --all && git push origin --tags", "postversion": "npm publish", "version": "make build && git add Changelog"}, "version": "0.3.24"}