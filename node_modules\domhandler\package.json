{"_from": "domhandler@^4.3.1", "_id": "domhand<PERSON>@4.3.1", "_inBundle": false, "_integrity": "sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==", "_location": "/domhandler", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "domhandler@^4.3.1", "name": "<PERSON><PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON><PERSON>", "rawSpec": "^4.3.1", "saveSpec": null, "fetchSpec": "^4.3.1"}, "_requiredBy": ["/htmlparser2", "/htmlparser2/dom-serializer", "/htmlparser2/domutils", "/renderkid/css-select", "/renderkid/dom-serializer", "/renderkid/domutils"], "_resolved": "http://*************:4873/domhandler/-/domhandler-4.3.1.tgz", "_shasum": "8d792033416f59d68bc03a5aa7b018c1ca89279c", "_spec": "domhandler@^4.3.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\renderkid\\node_modules\\css-select", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "bundleDependencies": false, "dependencies": {"domelementtype": "^2.2.0"}, "deprecated": false, "description": "Handler for htmlparser2 that turns pages into a dom", "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.21", "@typescript-eslint/eslint-plugin": "^5.15.0", "@typescript-eslint/parser": "^5.15.0", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^7.2.0", "jest": "^27.5.1", "prettier": "^2.6.0", "ts-jest": "^27.1.3", "typescript": "^4.6.2"}, "engines": {"node": ">= 4"}, "files": ["lib"], "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "homepage": "https://github.com/fb55/domhandler#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "keywords": ["dom", "htmlparser2"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "name": "<PERSON><PERSON><PERSON><PERSON>", "prettier": {"tabWidth": 4}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "scripts": {"build": "tsc", "format": "prettier --write '**/*.{ts,md,json}'", "lint": "eslint src", "prepare": "npm run build", "test": "npm run test:jest && npm run lint", "test:jest": "jest"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "4.3.1"}