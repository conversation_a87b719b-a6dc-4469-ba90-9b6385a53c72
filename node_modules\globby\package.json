{"_from": "globby@^9.2.0", "_id": "globby@9.2.0", "_inBundle": false, "_integrity": "sha512-ollPHROa5mcxDEkwg6bPt3QbEf4pDQSNtd6JPL1YvOvAo/7/0VAm9TccUeoTmarjPw4pfUthSCqcyfNB1I3ZSg==", "_location": "/globby", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "globby@^9.2.0", "name": "globby", "escapedName": "globby", "rawSpec": "^9.2.0", "saveSpec": null, "fetchSpec": "^9.2.0"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://192.168.3.141:4873/globby/-/globby-9.2.0.tgz", "_shasum": "fd029a706c703d29bdd170f4b6db3a3f7a7cb63d", "_spec": "globby@^9.2.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/globby/issues"}, "bundleDependencies": false, "dependencies": {"@types/glob": "^7.1.1", "array-union": "^1.0.2", "dir-glob": "^2.2.2", "fast-glob": "^2.2.6", "glob": "^7.1.3", "ignore": "^4.0.3", "pify": "^4.0.1", "slash": "^2.0.0"}, "deprecated": false, "description": "Extends `glob` with support for multiple patterns and exposes a Promise API", "devDependencies": {"ava": "^1.4.1", "glob-stream": "^6.1.0", "globby": "github:sindresorhus/globby#master", "matcha": "^0.7.0", "rimraf": "^2.6.3", "tsd": "^0.7.1", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "gitignore.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/globby#readme", "keywords": ["all", "array", "directories", "dirs", "expand", "files", "filesystem", "filter", "find", "fnmatch", "folders", "fs", "glob", "globbing", "globs", "gulpfriendly", "match", "matcher", "minimatch", "multi", "multiple", "paths", "pattern", "patterns", "traverse", "util", "utility", "wildcard", "wildcards", "promise", "gitignore", "git"], "license": "MIT", "name": "globby", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/globby.git"}, "scripts": {"bench": "npm update glob-stream fast-glob && matcha bench.js", "test": "xo && ava && tsd"}, "version": "9.2.0", "xo": {"ignores": ["fixtures"]}}