# Vue 2 AI聊天助手消息渲染功能

这是一个基于Vue 2的AI聊天助手项目，实现了完整的Markdown消息渲染功能，支持代码语法高亮和表格显示。

## 技术栈

- **Vue**: ^2.6.10 - 前端框架
- **marked**: ^1.1.0 - Markdown解析库
- **highlight.js**: ^9.18.1 - 代码语法高亮库

## 功能特性

### ✅ Markdown渲染支持
- 标题（H1-H6）
- 段落和换行
- 列表（有序和无序）
- 链接
- 粗体和斜体
- 行内代码和代码块
- 引用块
- 分割线
- 图片

### ✅ 代码语法高亮
- 支持多种编程语言
- JavaScript/TypeScript
- Python
- Java
- C++/C#
- HTML/CSS
- Vue/React
- 以及更多语言

### ✅ 表格渲染
- 完整的表格支持
- 表头样式
- 行悬停效果
- 响应式设计

### ✅ 聊天界面
- 用户和AI助手消息区分
- 消息时间显示
- 头像显示
- 实时消息输入
- 模拟AI回复

## 项目结构

```
vue-ai-chat-assistant/
├── public/
│   └── index.html              # HTML模板
├── src/
│   ├── components/
│   │   └── MessageRenderer.vue # 消息渲染组件
│   ├── App.vue                 # 主应用组件
│   └── main.js                 # 应用入口
├── package.json                # 项目配置
├── vue.config.js              # Vue配置
└── README.md                  # 项目说明
```

## 安装和运行

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run serve
```

### 3. 构建生产版本
```bash
npm run build
```

## 核心组件说明

### MessageRenderer组件

位置：`src/components/MessageRenderer.vue`

这是核心的消息渲染组件，负责将Markdown文本转换为HTML并应用语法高亮。

**主要功能：**
- 接收Markdown文本作为props
- 使用marked库解析Markdown
- 使用highlight.js进行代码高亮
- 应用白色主题样式

**使用方法：**
```vue
<MessageRenderer :content="markdownContent" />
```

### 样式特点

- **白色主题**：整体采用白色背景设计
- **现代化UI**：圆角、阴影、渐变等现代设计元素
- **响应式**：支持移动端和桌面端
- **代码高亮**：使用GitHub风格的代码高亮主题
- **表格美化**：带有悬停效果和边框的表格样式

## 示例数据

项目内置了丰富的示例数据，包括：

1. **欢迎消息**：展示基本Markdown语法
2. **表格示例**：API接口参数表格
3. **代码示例**：Vue组件和CSS代码
4. **交互功能**：用户可以发送消息触发不同的AI回复

## 自定义配置

### 修改代码高亮主题

在 `src/main.js` 中修改highlight.js主题：

```javascript
// 当前使用GitHub主题
import 'highlight.js/styles/github.css'

// 可以替换为其他主题，如：
// import 'highlight.js/styles/atom-one-dark.css'
// import 'highlight.js/styles/vs2015.css'
```

### 自定义样式

在 `src/components/MessageRenderer.vue` 的 `<style>` 部分修改样式变量和规则。

### 添加新的消息类型

在 `src/App.vue` 的 `generateAIResponse` 方法中添加新的消息处理逻辑。

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 开发说明

- 项目使用Vue CLI构建
- 支持热重载开发
- 代码采用ES6+语法
- 样式使用Scoped CSS

## 许可证

MIT License
