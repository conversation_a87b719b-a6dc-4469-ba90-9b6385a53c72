{"_from": "cssnano@^4.1.10", "_id": "cssnano@4.1.11", "_inBundle": false, "_integrity": "sha512-6gZm2htn7xIPJOHY824ERgj8cNPgPxyCSnkXc4v7YvNW+TdVfzgngHcEhy/8D11kUWRUMbke+tC+AUcUsnMz2g==", "_location": "/cssnano", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "cssnano@^4.1.10", "name": "cssnano", "escapedName": "cssnano", "rawSpec": "^4.1.10", "saveSpec": null, "fetchSpec": "^4.1.10"}, "_requiredBy": ["/@intervolga/optimize-cssnano-plugin", "/@vue/cli-service"], "_resolved": "http://*************:4873/cssnano/-/cssnano-4.1.11.tgz", "_shasum": "c7b5f5b81da269cb1fd982cb960c1200910c9a99", "_spec": "cssnano@^4.1.10", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"cosmiconfig": "^5.0.0", "cssnano-preset-default": "^4.0.8", "is-resolvable": "^1.0.0", "postcss": "^7.0.0"}, "deprecated": false, "description": "A modular minifier, built on top of the PostCSS ecosystem.", "devDependencies": {"array-to-sentence": "^2.0.0", "babel-cli": "^6.0.0", "babel-core": "^6.0.0", "babel-loader": "^7.0.0", "cross-env": "^5.0.0", "cssnano-preset-advanced": "^4.0.7", "postcss-font-magician": "^2.0.0", "webpack": "^2.0.0", "webpack-bundle-size-analyzer": "^2.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["dist", "LICENSE-MIT", "quickstart.js"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["css", "compress", "minify", "optimise", "optimisation", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "cssnano", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"bundle-size": "webpack --json --config src/__tests__/_webpack.config.js | webpack-bundle-size-analyzer", "integrations": "babel-node src/__tests__/util/rebuild.js", "prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "tonicExampleFilename": "quickstart.js", "version": "4.1.11"}