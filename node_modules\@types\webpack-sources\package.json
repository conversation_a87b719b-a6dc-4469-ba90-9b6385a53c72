{"_from": "@types/webpack-sources@*", "_id": "@types/webpack-sources@3.2.3", "_inBundle": false, "_integrity": "sha512-4nZOdMwSPHZ4pTEZzSp0AsTM4K7Qmu40UKW4tJDiOVs20UzYF9l+qUe4s0ftfN0pin06n+5cWWDJXH+sbhAiDw==", "_location": "/@types/webpack-sources", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/webpack-sources@*", "name": "@types/webpack-sources", "escapedName": "@types%2fwebpack-sources", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/webpack"], "_resolved": "http://192.168.3.141:4873/@types/webpack-sources/-/webpack-sources-3.2.3.tgz", "_shasum": "b667bd13e9fa15a9c26603dce502c7985418c3d8", "_spec": "@types/webpack-sources@*", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@types\\webpack", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "e-cloud", "url": "https://github.com/e-cloud"}, {"name": "<PERSON>", "url": "https://github.com/chrise<PERSON>tein"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "dependencies": {"@types/node": "*", "@types/source-list-map": "*", "source-map": "^0.7.3"}, "deprecated": false, "description": "TypeScript definitions for webpack-sources", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-sources", "license": "MIT", "main": "", "name": "@types/webpack-sources", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack-sources"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "7bdf9048654ee82707e648ea73eb0dbef57288a82d4076860bac7b3e738fd185", "version": "3.2.3"}