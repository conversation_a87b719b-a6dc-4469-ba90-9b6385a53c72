{"_from": "postcss-calc@^7.0.1", "_id": "postcss-calc@7.0.5", "_inBundle": false, "_integrity": "sha512-1tKHutbGtLtEZF6PT4JSihCHfIVldU72mZ8SdZHIYriIZ9fh9k9aWSppaT8rHsyI3dX+KSR+W+Ix9BMY3AODrg==", "_location": "/postcss-calc", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-calc@^7.0.1", "name": "postcss-calc", "escapedName": "postcss-calc", "rawSpec": "^7.0.1", "saveSpec": null, "fetchSpec": "^7.0.1"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "http://192.168.3.141:4873/postcss-calc/-/postcss-calc-7.0.5.tgz", "_shasum": "f8a6e99f12e619c2ebc23cf6c486fdc15860933e", "_spec": "postcss-calc@^7.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cssnano-preset-default", "author": {"name": "<PERSON>"}, "ava": {"require": ["@babel/register", "@babel/polyfill"]}, "bugs": {"url": "https://github.com/postcss/postcss-calc/issues"}, "bundleDependencies": false, "dependencies": {"postcss": "^7.0.27", "postcss-selector-parser": "^6.0.2", "postcss-value-parser": "^4.0.2"}, "deprecated": false, "description": "PostCSS plugin to reduce calc()", "devDependencies": {"@babel/cli": "^7.1.2", "@babel/core": "^7.1.2", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.1.0", "@babel/register": "^7.0.0", "ava": "^1.4.1", "babel-eslint": "^10.0.1", "babel-plugin-add-module-exports": "^1.0.0", "cross-env": "^5.2.0", "del-cli": "^1.1.0", "eslint": "^5.7.0", "eslint-config-i-am-meticulous": "^11.0.0", "eslint-plugin-babel": "^5.2.1", "eslint-plugin-import": "^2.14.0", "jison-gho": "^0.6.1-215"}, "eslintConfig": {"parser": "babel-es<PERSON>", "extends": "eslint-config-i-am-meticulous", "rules": {"curly": "error"}}, "files": ["dist", "LICENSE"], "homepage": "https://github.com/postcss/postcss-calc#readme", "keywords": ["css", "postcss", "postcss-plugin", "calculation", "calc"], "license": "MIT", "main": "dist/index.js", "name": "postcss-calc", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-calc.git"}, "scripts": {"build": "del-cli dist && cross-env BABEL_ENV=publish babel src --out-dir dist --ignore src/__tests__/**/*.js && jison src/parser.jison -o dist/parser.js", "prepublish": "npm run build", "pretest": "npm run build && eslint src", "test": "ava"}, "version": "7.0.5"}