{"_from": "default-gateway@^5.0.5", "_id": "default-gateway@5.0.5", "_inBundle": false, "_integrity": "sha512-z2RnruVmj8hVMmAnEJMTIJNijhKCDiGjbLP+BHJFOT7ld3Bo5qcIBpVYDniqhbMIIf+jZDlkP2MkPXiQy/DBLA==", "_location": "/default-gateway", "_phantomChildren": {"human-signals": "1.1.1", "isexe": "2.0.0", "merge-stream": "2.0.0", "pump": "3.0.3", "signal-exit": "3.0.7", "strip-final-newline": "2.0.0"}, "_requested": {"type": "range", "registry": true, "raw": "default-gateway@^5.0.5", "name": "default-gateway", "escapedName": "default-gateway", "rawSpec": "^5.0.5", "saveSpec": null, "fetchSpec": "^5.0.5"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "http://*************:4873/default-gateway/-/default-gateway-5.0.5.tgz", "_shasum": "4fd6bd5d2855d39b34cc5a59505486e9aafc9b10", "_spec": "default-gateway@^5.0.5", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-service", "author": {"name": "silverwind", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/silverwind/default-gateway/issues"}, "bundleDependencies": false, "dependencies": {"execa": "^3.3.0"}, "deprecated": false, "description": "Get the default network gateway, cross-platform.", "devDependencies": {"eslint": "6.6.0", "eslint-config-silverwind": "5.0.0", "updates": "9.0.1", "ver": "6.0.2"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "files": ["index.js", "android.js", "darwin.js", "freebsd.js", "linux.js", "openbsd.js", "sunos.js", "win32.js", "ibmi.js"], "homepage": "https://github.com/silverwind/default-gateway#readme", "keywords": ["default gateway", "network", "default", "gateway", "routing", "route"], "license": "BSD-2-<PERSON><PERSON>", "name": "default-gateway", "repository": {"type": "git", "url": "git+https://github.com/silverwind/default-gateway.git"}, "scripts": {"test": "eslint *.js && node --pending-deprecation --trace-deprecation --throw-deprecation --trace-warnings test.js"}, "version": "5.0.5"}