{"_from": "ora@^3.4.0", "_id": "ora@3.4.0", "_inBundle": false, "_integrity": "sha512-eNwHudNbO1folBP3JsZ19v9azXWtQZjICdr3Q0TDPIaeBQ3mXLrh54wM+er0+hSp+dWKf+Z8KM58CYzEyIYxYg==", "_location": "/ora", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ora@^3.4.0", "name": "ora", "escapedName": "ora", "rawSpec": "^3.4.0", "saveSpec": null, "fetchSpec": "^3.4.0"}, "_requiredBy": ["/@vue/cli-shared-utils"], "_resolved": "http://*************:4873/ora/-/ora-3.4.0.tgz", "_shasum": "bf0752491059a3ef3ed4c85097531de9fdbcd318", "_spec": "ora@^3.4.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\@vue\\cli-shared-utils", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/ora/issues"}, "bundleDependencies": false, "dependencies": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1"}, "deprecated": false, "description": "Elegant terminal spinner", "devDependencies": {"@types/node": "^11.13.0", "ava": "^1.4.1", "get-stream": "^4.1.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/ora#readme", "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "license": "MIT", "name": "ora", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/ora.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.4.0"}