{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\test\\view\\src\\App.vue?vue&type=style&index=0&id=7ba5bd90&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\src\\App.vue", "mtime": 1754029653454}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAoPA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <div id=\"app\">\n    <div class=\"chat-container\">\n      <div class=\"chat-header\">\n        <h1>AI聊天助手</h1>\n        <p>支持Markdown渲染和代码语法高亮</p>\n      </div>\n      \n      <div class=\"chat-messages\">\n        <div \n          v-for=\"(message, index) in messages\" \n          :key=\"index\"\n          class=\"message-wrapper\"\n          :class=\"message.type\"\n        >\n          <div class=\"message-avatar\">\n            <span v-if=\"message.type === 'user'\">👤</span>\n            <span v-else>🤖</span>\n          </div>\n          \n          <div class=\"message-content\">\n            <div class=\"message-header\">\n              <span class=\"sender-name\">{{ message.sender }}</span>\n              <span class=\"message-time\">{{ message.time }}</span>\n            </div>\n            \n            <MessageRenderer \n              v-if=\"message.type === 'assistant'\"\n              :content=\"message.content\"\n            />\n            <div v-else class=\"user-message\">\n              {{ message.content }}\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"chat-input\">\n        <input \n          v-model=\"newMessage\"\n          @keyup.enter=\"sendMessage\"\n          placeholder=\"输入消息...\"\n          class=\"message-input\"\n        />\n        <button @click=\"sendMessage\" class=\"send-button\">发送</button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport MessageRenderer from './components/MessageRenderer.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    MessageRenderer\n  },\n  data() {\n    return {\n      newMessage: '',\n      messages: [\n        {\n          type: 'assistant',\n          sender: 'AI助手',\n          time: '10:30',\n          content: `# 欢迎使用AI聊天助手！\n\n我可以帮助您处理各种问题，支持**Markdown**格式和代码语法高亮。\n\n## 功能特性\n\n- ✅ **Markdown渲染**：支持标题、列表、表格等\n- ✅ **代码高亮**：支持多种编程语言\n- ✅ **表格显示**：清晰的数据展示\n- ✅ **链接支持**：[点击访问GitHub](https://github.com)\n\n### 代码示例\n\n\\`\\`\\`javascript\nfunction greet(name) {\n  console.log(\\`Hello, \\${name}!\\`);\n  return \\`欢迎使用AI助手，\\${name}！\\`;\n}\n\ngreet('开发者');\n\\`\\`\\`\n\n### Python示例\n\n\\`\\`\\`python\ndef calculate_fibonacci(n):\n    if n <= 1:\n        return n\n    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)\n\n# 计算斐波那契数列\nfor i in range(10):\n    print(f\"F({i}) = {calculate_fibonacci(i)}\")\n\\`\\`\\``\n        },\n        {\n          type: 'user',\n          sender: '用户',\n          time: '10:32',\n          content: '请展示一个表格示例'\n        }\n      ]\n    }\n  },\n  methods: {\n    sendMessage() {\n      if (this.newMessage.trim()) {\n        // 添加用户消息\n        this.messages.push({\n          type: 'user',\n          sender: '用户',\n          time: this.getCurrentTime(),\n          content: this.newMessage\n        })\n        \n        // 模拟AI回复\n        setTimeout(() => {\n          this.messages.push({\n            type: 'assistant',\n            sender: 'AI助手',\n            time: this.getCurrentTime(),\n            content: this.generateAIResponse(this.newMessage)\n          })\n        }, 1000)\n        \n        this.newMessage = ''\n      }\n    },\n    \n    getCurrentTime() {\n      const now = new Date()\n      return `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`\n    },\n    \n    generateAIResponse(userMessage) {\n      if (userMessage.includes('表格')) {\n        return this.getTableExample()\n      } else if (userMessage.includes('代码')) {\n        return this.getCodeExample()\n      } else {\n        return this.getGeneralResponse()\n      }\n    },\n    \n    getTableExample() {\n      return `## API接口参数表格\n\n| 字段英文名 | 字段中文名 | 业务口径 |\n|-----------|-----------|---------|\n| sourceSystemId | 相户ID | 用于验证调用方是否有权限的数据，由系统管理员分配，可发邮件给 ************************进行申请 |\n| sourceSystemName | 相户名称 | 用于验证调用方是否有权限的数据，由系统管理员分配，可发邮件给 ************************进行申请 |\n| serviceFlag | 服务返回标识 | 用于标识此次请求是否返回成功，成功为TRUE，失败为FALSE |\n| returnCode | 服务返回码 | 用于说明返回成功或失败后具体的原因代码 |\n\n### 基础信息表格\n\n| API编号 | VGOP编号 | API英文名 | 一级业务标签 | 二级业务标签 |\n|---------|----------|-----------|-------------|-------------|\n| IGL001001 | AME0002500 | regionMng_addRegionShape | 功能管理 | 区域管理 |`\n    },\n    \n    getCodeExample() {\n      return `## 代码示例\n\n### Vue组件示例\n\n\\`\\`\\`vue\n<template>\n  <div class=\"example-component\">\n    <h2>{{ title }}</h2>\n    <ul>\n      <li v-for=\"item in items\" :key=\"item.id\">\n        {{ item.name }}\n      </li>\n    </ul>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ExampleComponent',\n  data() {\n    return {\n      title: '示例组件',\n      items: [\n        { id: 1, name: '项目1' },\n        { id: 2, name: '项目2' }\n      ]\n    }\n  }\n}\n</script>\n\\`\\`\\`\n\n### CSS样式示例\n\n\\`\\`\\`css\n.example-component {\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background-color: #fff;\n}\n\n.example-component h2 {\n  color: #333;\n  margin-bottom: 16px;\n}\n\\`\\`\\``\n    },\n    \n    getGeneralResponse() {\n      return `感谢您的消息！我是AI助手，可以帮助您：\n\n## 主要功能\n\n1. **文档解析**：支持Markdown格式文档\n2. **代码展示**：多语言语法高亮\n3. **数据表格**：清晰的表格展示\n4. **技术支持**：编程相关问题解答\n\n### 支持的编程语言\n\n- JavaScript / TypeScript\n- Python\n- Java\n- C++ / C#\n- HTML / CSS\n- Vue / React\n- 以及更多...\n\n有什么具体问题需要帮助吗？`\n    }\n  }\n}\n</script>\n\n<style>\n#app {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n}\n\n.chat-container {\n  max-width: 800px;\n  margin: 0 auto;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  height: 90vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.chat-header {\n  background: #f8f9fa;\n  padding: 20px;\n  border-bottom: 1px solid #e9ecef;\n  text-align: center;\n}\n\n.chat-header h1 {\n  color: #333;\n  margin-bottom: 8px;\n  font-size: 24px;\n}\n\n.chat-header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.chat-messages {\n  flex: 1;\n  overflow-y: auto;\n  padding: 20px;\n  background: #fff;\n}\n\n.message-wrapper {\n  display: flex;\n  margin-bottom: 20px;\n  align-items: flex-start;\n}\n\n.message-wrapper.user {\n  flex-direction: row-reverse;\n}\n\n.message-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n  margin: 0 12px;\n  flex-shrink: 0;\n}\n\n.message-content {\n  max-width: 70%;\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 12px 16px;\n  position: relative;\n}\n\n.user .message-content {\n  background: #007bff;\n  color: white;\n}\n\n.message-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n  font-size: 12px;\n}\n\n.sender-name {\n  font-weight: bold;\n  color: #333;\n}\n\n.user .sender-name {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.message-time {\n  color: #999;\n}\n\n.user .message-time {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.user-message {\n  line-height: 1.5;\n}\n\n.chat-input {\n  display: flex;\n  padding: 20px;\n  background: #f8f9fa;\n  border-top: 1px solid #e9ecef;\n}\n\n.message-input {\n  flex: 1;\n  padding: 12px 16px;\n  border: 1px solid #ddd;\n  border-radius: 24px;\n  outline: none;\n  font-size: 14px;\n}\n\n.message-input:focus {\n  border-color: #007bff;\n}\n\n.send-button {\n  margin-left: 12px;\n  padding: 12px 24px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 24px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: background-color 0.2s;\n}\n\n.send-button:hover {\n  background: #0056b3;\n}\n</style>\n"]}]}