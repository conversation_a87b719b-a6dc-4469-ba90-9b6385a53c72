<template>
  <div id="app">
    <div class="chat-container">
      <div class="chat-header">
        <h1>AI聊天助手</h1>
        <p>支持Markdown渲染和代码语法高亮</p>
      </div>

      <div class="chat-messages">
        <div
          v-for="(message, index) in messages"
          :key="index"
          class="message-wrapper"
          :class="message.type"
        >
          <div class="message-avatar">
            <span v-if="message.type === 'user'">👤</span>
            <span v-else>🤖</span>
          </div>

          <div class="message-content">
            <div class="message-header">
              <span class="sender-name">{{ message.sender }}</span>
              <span class="message-time">{{ message.time }}</span>
            </div>

            <MessageRenderer
              v-if="message.type === 'assistant'"
              :content="message.content"
            />
            <div v-else class="user-message">
              {{ message.content }}
            </div>
          </div>
        </div>
      </div>

      <div class="chat-input">
        <input
          v-model="newMessage"
          @keyup.enter="sendMessage"
          placeholder="输入消息..."
          class="message-input"
        />
        <button @click="sendMessage" class="send-button">发送</button>
      </div>
    </div>
  </div>
</template>

<script>
import MessageRenderer from "./components/MessageRenderer.vue";

export default {
  name: "App",
  components: {
    MessageRenderer,
  },
  data() {
    return {
      newMessage: "",
      messages: [
        {
          type: "assistant",
          sender: "AI助手",
          time: "10:30",
          content: `# 欢迎使用AI聊天助手！

我可以帮助您处理各种问题，支持**Markdown**格式和代码语法高亮。

## 功能特性

- ✅ **Markdown渲染**：支持标题、列表、表格等
- ✅ **代码高亮**：支持多种编程语言
- ✅ **表格显示**：清晰的数据展示
- ✅ **链接支持**：[点击访问GitHub](https://github.com)

### 代码示例

\`\`\`javascript
function greet(name) {
  console.log(\`Hello, \${name}!\`);
  return \`欢迎使用AI助手，\${name}！\`;
}

greet('开发者');
\`\`\`

### Python示例

\`\`\`python
def calculate_fibonacci(n):
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

# 计算斐波那契数列
for i in range(10):
    print(f"F({i}) = {calculate_fibonacci(i)}")
\`\`\``,
        },
        {
          type: "user",
          sender: "用户",
          time: "10:32",
          content: "请展示一个表格示例",
        },
      ],
    };
  },
  methods: {
    sendMessage() {
      if (this.newMessage.trim()) {
        // 添加用户消息
        this.messages.push({
          type: "user",
          sender: "用户",
          time: this.getCurrentTime(),
          content: this.newMessage,
        });

        // 模拟AI回复
        setTimeout(() => {
          this.messages.push({
            type: "assistant",
            sender: "AI助手",
            time: this.getCurrentTime(),
            content: this.generateAIResponse(this.newMessage),
          });
        }, 1000);

        this.newMessage = "";
      }
    },

    getCurrentTime() {
      const now = new Date();
      return `${now.getHours()}:${now
        .getMinutes()
        .toString()
        .padStart(2, "0")}`;
    },

    generateAIResponse(userMessage) {
      if (userMessage.includes("表格")) {
        return this.getTableExample();
      } else if (userMessage.includes("代码")) {
        return this.getCodeExample();
      } else {
        return this.getGeneralResponse();
      }
    },

    getTableExample() {
      return `## API接口参数表格

| 字段英文名 | 字段中文名 | 业务口径 |
|-----------|-----------|---------|
| sourceSystemId | 相户ID | 用于验证调用方是否有权限的数据，由系统管理员分配，可发邮件给 ************************进行申请 |
| sourceSystemName | 相户名称 | 用于验证调用方是否有权限的数据，由系统管理员分配，可发邮件给 ************************进行申请 |
| serviceFlag | 服务返回标识 | 用于标识此次请求是否返回成功，成功为TRUE，失败为FALSE |
| returnCode | 服务返回码 | 用于说明返回成功或失败后具体的原因代码 |

### 基础信息表格

| API编号 | VGOP编号 | API英文名 | 一级业务标签 | 二级业务标签 |
|---------|----------|-----------|-------------|-------------|
| IGL001001 | AME0002500 | regionMng_addRegionShape | 功能管理 | 区域管理 |`;
    },

    getCodeExample() {
      return `## 代码示例

### Vue组件示例

\`\`\`vue
<template>
  <div class="example-component">
    <h2>{{ title }}</h2>
    <ul>
      <li v-for="item in items" :key="item.id">
        {{ item.name }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'ExampleComponent',
  data() {
    return {
      title: '示例组件',
      items: [
        { id: 1, name: '项目1' },
        { id: 2, name: '项目2' }
      ]
    }
  }
}
<\/script>
\`\`\`

### CSS样式示例

\`\`\`css
.example-component {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
}

.example-component h2 {
  color: #333;
  margin-bottom: 16px;
}
\`\`\``;
    },

    getGeneralResponse() {
      return `感谢您的消息！我是AI助手，可以帮助您：

## 主要功能

1. **文档解析**：支持Markdown格式文档
2. **代码展示**：多语言语法高亮
3. **数据表格**：清晰的表格展示
4. **技术支持**：编程相关问题解答

### 支持的编程语言

- JavaScript / TypeScript
- Python
- Java
- C++ / C#
- HTML / CSS
- Vue / React
- 以及更多...

有什么具体问题需要帮助吗？`;
    },
  },
};
</script>

<style>
#app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.chat-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 90vh;
  display: flex;
  flex-direction: column;
}

.chat-header {
  background: #f8f9fa;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  text-align: center;
}

.chat-header h1 {
  color: #333;
  margin-bottom: 8px;
  font-size: 24px;
}

.chat-header p {
  color: #666;
  font-size: 14px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #fff;
}

.message-wrapper {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
}

.message-wrapper.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin: 0 12px;
  flex-shrink: 0;
}

.message-content {
  max-width: 70%;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 12px 16px;
  position: relative;
}

.user .message-content {
  background: #007bff;
  color: white;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.sender-name {
  font-weight: bold;
  color: #333;
}

.user .sender-name {
  color: rgba(255, 255, 255, 0.9);
}

.message-time {
  color: #999;
}

.user .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.user-message {
  line-height: 1.5;
}

.chat-input {
  display: flex;
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 24px;
  outline: none;
  font-size: 14px;
}

.message-input:focus {
  border-color: #007bff;
}

.send-button {
  margin-left: 12px;
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 24px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.send-button:hover {
  background: #0056b3;
}
</style>
