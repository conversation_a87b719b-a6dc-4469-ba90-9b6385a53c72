{"_from": "xtend@~4.0.1", "_id": "xtend@4.0.2", "_inBundle": false, "_integrity": "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==", "_location": "/xtend", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "xtend@~4.0.1", "name": "xtend", "escapedName": "xtend", "rawSpec": "~4.0.1", "saveSpec": null, "fetchSpec": "~4.0.1"}, "_requiredBy": ["/stream-http", "/through2"], "_resolved": "http://*************:4873/xtend/-/xtend-4.0.2.tgz", "_shasum": "bb72779f5fa465186b1f438f674fa347fdb5db54", "_spec": "xtend@~4.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\through2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Raynos/xtend/issues", "email": "<EMAIL>"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "dependencies": {}, "deprecated": false, "description": "extend like a boss", "devDependencies": {"tape": "~1.1.0"}, "engines": {"node": ">=0.4"}, "homepage": "https://github.com/Raynos/xtend", "keywords": ["extend", "merge", "options", "opts", "object", "array"], "license": "MIT", "main": "immutable", "name": "xtend", "repository": {"type": "git", "url": "git://github.com/Raynos/xtend.git"}, "scripts": {"test": "node test"}, "testling": {"files": "test.js", "browsers": ["ie/7..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "version": "4.0.2"}