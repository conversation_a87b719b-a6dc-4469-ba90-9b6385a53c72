{"_from": "postcss-modules-scope@^2.2.0", "_id": "postcss-modules-scope@2.2.0", "_inBundle": false, "_integrity": "sha512-YyEgsTMRpNd+HmyC7H/mh3y+MeFWevy7V1evVhJWewmMbjDHIbZbOXICC2y+m1xI1UVfIT1HMW/O04Hxyu9oXQ==", "_location": "/postcss-modules-scope", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "postcss-modules-scope@^2.2.0", "name": "postcss-modules-scope", "escapedName": "postcss-modules-scope", "rawSpec": "^2.2.0", "saveSpec": null, "fetchSpec": "^2.2.0"}, "_requiredBy": ["/css-loader"], "_resolved": "http://*************:4873/postcss-modules-scope/-/postcss-modules-scope-2.2.0.tgz", "_shasum": "385cae013cc7743f5a7d7602d1073a89eaae62ee", "_spec": "postcss-modules-scope@^2.2.0", "_where": "C:\\Users\\<USER>\\Desktop\\test\\view\\node_modules\\css-loader", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/css-modules/postcss-modules-scope/issues"}, "bundleDependencies": false, "dependencies": {"postcss": "^7.0.6", "postcss-selector-parser": "^6.0.0"}, "deprecated": false, "description": "A CSS Modules transform to extract export statements from local-scope classes", "devDependencies": {"chokidar-cli": "^1.0.1", "codecov.io": "^0.1.2", "coveralls": "^3.0.2", "cssesc": "^3.0.0", "eslint": "^5.9.0", "mocha": "^6.0.2", "nyc": "^14.1.0"}, "engines": {"node": ">= 6"}, "files": ["src"], "homepage": "https://github.com/css-modules/postcss-modules-scope", "keywords": ["css-modules", "postcss", "plugin"], "license": "ISC", "main": "src/index.js", "name": "postcss-modules-scope", "prettier": {"semi": true, "singleQuote": true, "trailingComma": "es5"}, "repository": {"type": "git", "url": "git+https://github.com/css-modules/postcss-modules-scope.git"}, "scripts": {"autotest": "chokidar src test -c 'yarn test'", "cover": "nyc mocha", "lint": "eslint src test", "precover": "yarn lint", "prepublish": "yarn run test", "pretest": "yarn lint", "test": "mocha", "travis": "yarn cover"}, "version": "2.2.0"}